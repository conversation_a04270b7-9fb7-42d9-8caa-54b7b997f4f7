import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { KvKPhase } from '../types/dataTypes';
import { createKvK } from '../services/kvkService';

interface FormData {
  name: string;
  season: number;
  startDate: string;
  kvkPhase: KvKPhase;
  baselineScan: File | null;
  baselineScanName: string;
}

const CreateKvKPage: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize form data
  const [formData, setFormData] = useState<FormData>({
    name: '',
    season: 1,
    startDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
    kvkPhase: KvKPhase.PreKvK,
    baselineScan: null,
    baselineScanName: 'Baseline Scan'
  });

  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'season' ? parseInt(value) : value
    }));
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setError(null);

    if (!file) {
      setFormData(prev => ({ ...prev, baselineScan: null }));
      return;
    }

    // Check file extension
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const validExtensions = ['xlsx', 'xls', 'csv'];

    if (!fileExtension || !validExtensions.includes(fileExtension)) {
      setError(`Please upload a valid Excel or CSV file. Received file with extension: ${fileExtension || 'unknown'}`);
      setFormData(prev => ({ ...prev, baselineScan: null }));
      if (fileInputRef.current) fileInputRef.current.value = '';
      return;
    }

    // Set the file in state
    setFormData(prev => ({ ...prev, baselineScan: file }));
  };

  // Create KvK mutation
  const createKvKMutation = useMutation({
    mutationFn: async () => {
      try {
        // Validate form data
        if (!formData.name.trim()) {
          throw new Error('KvK name is required');
        }

        if (formData.season < 1) {
          throw new Error('Season must be a positive number');
        }

        // Create new KvK
        const newKvK = await createKvK(
          formData.name,
          formData.startDate,
          formData.season
        );

        // If a baseline scan was provided, upload it
        if (formData.baselineScan) {
          console.log('Uploading baseline scan for KvK:', newKvK.id);

          const uploadFormData = new FormData();
          uploadFormData.append('file', formData.baselineScan);

          const uploadUrl = `/api/scans/upload?scan_name=${encodeURIComponent(formData.baselineScanName)}&is_baseline=true&kvk_id=${newKvK.id}`;

          const uploadResponse = await fetch(uploadUrl, {
            method: 'POST',
            body: uploadFormData,
          });

          if (!uploadResponse.ok) {
            const errorText = await uploadResponse.text();
            throw new Error(`Failed to upload baseline scan: ${errorText}`);
          }

          const scanData = await uploadResponse.json();
          console.log('Baseline scan uploaded successfully:', scanData);
        }

        return newKvK;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data) => {
      // Set success message based on whether baseline scan was uploaded
      const message = formData.baselineScan
        ? 'KvK created and baseline scan uploaded successfully!'
        : 'KvK created successfully!';
      setSuccess(message);

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['kvkList'] });
      queryClient.invalidateQueries({ queryKey: ['scansList'] });

      // Wait a moment to show the success message before redirecting
      setTimeout(() => {
        navigate(`/kvk/${data.id}`);
      }, 2000);
    },
    onError: (error: Error) => {
      setError(`Failed to create KvK: ${error.message}`);
    }
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    createKvKMutation.mutate();
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <header className="mb-8">
        <h1 className={`text-3xl font-bold mb-2 ${theme === 'light' ? 'text-blue-600' : 'text-blue-400'}`}>
          Create New KvK
        </h1>
        <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
          Set up a new Kingdom vs Kingdom event and upload a baseline scan
        </p>
      </header>

      {success && (
        <div className="mb-6 p-4 rounded-md bg-green-50 border border-green-200">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Success</h3>
              <div className="mt-2 text-sm text-green-700">
                <p>{success}</p>
                <p className="mt-1">Redirecting to KvK page...</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="mb-6 p-4 rounded-md bg-red-50 border border-red-200">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className={`p-6 ${theme === 'light' ? 'bg-white' : 'bg-gray-800'} rounded-lg shadow-md border ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* KvK Name */}
            <div>
              <label htmlFor="name" className={`block text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'} mb-1`}>
                KvK Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                placeholder="e.g., Heroic Anthem"
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  theme === 'light'
                    ? 'bg-white border-gray-300 text-gray-900'
                    : 'bg-gray-700 border-gray-600 text-white'
                }`}
              />
            </div>

            {/* Season */}
            <div>
              <label htmlFor="season" className={`block text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'} mb-1`}>
                Season Number *
              </label>
              <input
                type="number"
                id="season"
                name="season"
                value={formData.season}
                onChange={handleInputChange}
                required
                min="1"
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  theme === 'light'
                    ? 'bg-white border-gray-300 text-gray-900'
                    : 'bg-gray-700 border-gray-600 text-white'
                }`}
              />
            </div>

            {/* Start Date */}
            <div>
              <label htmlFor="startDate" className={`block text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'} mb-1`}>
                Start Date *
              </label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={formData.startDate}
                onChange={handleInputChange}
                required
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  theme === 'light'
                    ? 'bg-white border-gray-300 text-gray-900'
                    : 'bg-gray-700 border-gray-600 text-white'
                }`}
              />
            </div>

            {/* KvK Phase */}
            <div>
              <label htmlFor="kvkPhase" className={`block text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'} mb-1`}>
                Initial Phase *
              </label>
              <select
                id="kvkPhase"
                name="kvkPhase"
                value={formData.kvkPhase}
                onChange={handleInputChange}
                required
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  theme === 'light'
                    ? 'bg-white border-gray-300 text-gray-900'
                    : 'bg-gray-700 border-gray-600 text-white'
                }`}
              >
                {Object.values(KvKPhase).map(phase => (
                  <option key={phase} value={phase}>{phase}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Baseline Scan Section */}
          <div className="border-t pt-6 mt-6">
            <h3 className={`text-lg font-medium mb-4 ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
              Baseline Scan (Optional)
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
              {/* Baseline Scan Name */}
              <div>
                <label htmlFor="baselineScanName" className={`block text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'} mb-1`}>
                  Scan Name
                </label>
                <input
                  type="text"
                  id="baselineScanName"
                  name="baselineScanName"
                  value={formData.baselineScanName}
                  onChange={handleInputChange}
                  placeholder="Baseline Scan"
                  className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                    theme === 'light'
                      ? 'bg-white border-gray-300 text-gray-900'
                      : 'bg-gray-700 border-gray-600 text-white'
                  }`}
                />
              </div>
            </div>

            {/* File Upload */}
            <div>
              <label htmlFor="baselineScan" className={`block text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'} mb-1`}>
                Upload Baseline Scan (Excel or CSV)
              </label>
              <div className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md ${
                theme === 'light'
                  ? 'border-gray-300 bg-gray-50'
                  : 'border-gray-600 bg-gray-900'
              }`}>
                <div className="space-y-1 text-center">
                  <svg
                    className={`mx-auto h-12 w-12 ${theme === 'light' ? 'text-gray-400' : 'text-gray-500'}`}
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 48 48"
                    aria-hidden="true"
                  >
                    <path
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                      strokeWidth={2}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="flex text-sm text-center justify-center">
                    <label
                      htmlFor="file-upload"
                      className={`relative cursor-pointer rounded-md font-medium ${
                        theme === 'light' ? 'text-blue-600 hover:text-blue-500' : 'text-blue-400 hover:text-blue-300'
                      }`}
                    >
                      <span>Upload a file</span>
                      <input
                        id="file-upload"
                        name="file-upload"
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        accept=".xlsx,.xls,.csv"
                        className="sr-only"
                      />
                    </label>
                    <p className={`pl-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                      or drag and drop
                    </p>
                  </div>
                  <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                    Excel or CSV files only
                  </p>
                  {formData.baselineScan && (
                    <p className={`text-sm ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`}>
                      Selected: {formData.baselineScan.name}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={createKvKMutation.isPending}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                createKvKMutation.isPending ? 'opacity-75 cursor-not-allowed' : ''
              }`}
            >
              {createKvKMutation.isPending ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating...
                </>
              ) : 'Create KvK'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateKvKPage;
