from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from database import engine, Base, recreate_tables
from routers import players, scans, reports, system, kvks, auth # Added kvks and auth routers
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request

# Recreate tables to ensure schema is up to date
recreate_tables()

app = FastAPI(
    title="Data Website API",
    description="API for tracking player and alliance data.",
    version="0.1.0",
    docs_url="/docs", # Explicitly set docs URL
    redoc_url="/redoc", # Explicitly set redoc URL
    # Increase max request size to 100MB for large Excel files
    max_request_size=100 * 1024 * 1024
)

# Add startup event to initialize default parameters
# Note: @app.on_event is deprecated in FastAPI 0.104+, but still functional
@app.on_event("startup")
async def startup_event():
    """Initialize default parameters on startup."""
    from database import get_db
    import crud

    db = next(get_db())
    try:
        crud.initialize_default_parameters(db)
    finally:
        db.close()

# CORS (Cross-Origin Resource Sharing) middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins for development, restrict in production
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Add middleware to handle large file uploads
class LargeUploadMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Set max upload size to 100MB
        request._body_size_limit = 100 * 1024 * 1024
        response = await call_next(request)
        return response

app.add_middleware(LargeUploadMiddleware)

# Include routers with /api prefix
app.include_router(players.router, prefix="/api")
app.include_router(scans.router, prefix="/api")
app.include_router(kvks.router, prefix="/api") # Added KvK router
app.include_router(auth.router, prefix="/api") # Added auth router
# app.include_router(alliances.router) # Removed as alliances.py does not exist
# app.include_router(admin.router) # Removed as admin.py does not exist
app.include_router(reports.router, prefix="/api")
app.include_router(system.router, prefix="/api") # Ensure system router is included

# Root endpoint to redirect to API docs
@app.get("/", include_in_schema=False)
async def root():
    return RedirectResponse(url="/docs")

# Example of a simple health check endpoint (optional)
@app.get("/health", tags=["System"])
async def health_check():
    return {"status": "healthy"}