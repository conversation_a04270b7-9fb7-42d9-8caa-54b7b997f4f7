from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

# DATABASE_URL = "sqlite:///./kvk_tracker.db" # For SQLite
# DATABASE_URL = "postgresql://user:password@host:port/database" # For PostgreSQL

# Prioritize DATABASE_URL from environment variable if available (for production/docker)
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./kvk_tracker.db")

if DATABASE_URL.startswith("postgres"):
    engine = create_engine(DATABASE_URL)
else:
    # For SQLite, connect_args is needed for FastAPI/Uvicorn compatibility
    engine = create_engine(
        DATABASE_URL, connect_args={"check_same_thread": False}
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# Function to recreate all tables (useful for development)
def recreate_tables():
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()