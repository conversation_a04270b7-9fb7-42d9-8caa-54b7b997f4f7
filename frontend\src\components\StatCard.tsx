import React, { memo } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface StatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  change?: number;
  formatter?: (value: number) => string;
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon, 
  change,
  formatter = (val: number) => val.toLocaleString()
}) => {
  const { theme } = useTheme();
  
  // Format the value if it's a number
  const displayValue = typeof value === 'number' ? formatter(value) : value;
  
  return (
    <div className={`p-6 rounded-lg shadow-md transition-all duration-300 ${
      theme === 'light' ? 'bg-white hover:shadow-lg' : 'bg-gray-800 hover:shadow-gray-700'
    }`}>
      <div className="flex justify-between items-start">
        <div>
          <h2 className={`text-lg font-medium mb-1 ${
            theme === 'light' ? 'text-gray-700' : 'text-gray-300'
          }`}>
            {title}
          </h2>
          <p className={`text-2xl font-bold ${
            theme === 'light' ? 'text-blue-600' : 'text-blue-400'
          }`}>
            {displayValue}
          </p>
          
          {change !== undefined && (
            <p className={`mt-2 text-sm flex items-center ${
              change >= 0 
                ? theme === 'light' ? 'text-green-600' : 'text-green-400'
                : theme === 'light' ? 'text-red-600' : 'text-red-400'
            }`}>
              {change >= 0 ? (
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              ) : (
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              )}
              {Math.abs(change).toFixed(1)}%
            </p>
          )}
        </div>
        
        {icon && (
          <div className={`p-3 rounded-full ${
            theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-300'
          }`}>
            {icon}
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(StatCard);
