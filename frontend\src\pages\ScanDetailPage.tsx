import React from 'react';
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useTheme } from '../contexts/ThemeContext';
import { fetchScanById } from '../services/scanService';
import { formatNumber } from '../services/performanceService';
import { ScanData } from '../types/dataTypes';

const ScanDetailPage: React.FC = () => {
  const { theme } = useTheme(); // Keep useTheme for now, might be used elsewhere or for explicit toggles
  const { scanId } = useParams<{ scanId: string }>();

  const {
    data: scanDetails,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['scanDetails', scanId],
    queryFn: () => fetchScanById(scanId || ''),
    enabled: !!scanId, // Only run query if scanId is available
  });

  if (isLoading) return (
    <div className="text-center py-10 text-gray-700 dark:text-gray-300">
      <div className="animate-pulse">Loading scan data...</div>
    </div>
  );

  if (error) return (
    <div className="text-center py-10 bg-red-50 text-red-600 rounded-lg p-4 max-w-2xl mx-auto border border-red-200">
      <p className="font-semibold">Error fetching scan details:</p>
      <p>{(error as Error).message}</p>
    </div>
  );

  if (!scanDetails) return (
    <div className="text-center py-10 text-gray-700 dark:text-gray-300">
      Scan not found.
    </div>
  );

  return (
    <div className="max-w-6xl mx-auto p-4">
      <header className="mb-8">
        <Link
          to="/scans"
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-flex items-center"
        >
          &larr; Back to Scans List
        </Link>
        <h1 className="text-3xl font-bold text-blue-600 dark:text-blue-400 mt-4">
          {scanDetails?.name}
        </h1>
        <div className="flex flex-wrap items-center gap-2 mt-2 text-gray-600 dark:text-gray-300">
          <span className="flex items-center">
            <span className="font-medium mr-1">Date:</span>
            {scanDetails?.date ? new Date(scanDetails.date).toLocaleDateString() : 'N/A'}
          </span>
          <span className="mx-2">•</span>
          <span className="flex items-center">
            <span className="font-medium mr-1">KvK Phase:</span>
            <span className="font-semibold">
              {scanDetails?.kvkPhase}
            </span>
          </span>
          <span className="mx-2">•</span>
          <span className="flex items-center">
            <span className="font-medium mr-1">Baseline:</span>
            <span className={`font-semibold ${ // Conditional class for baseline status color
              scanDetails?.isBaseline
                ? 'text-green-600 dark:text-green-400'
                : 'text-gray-700 dark:text-gray-400'
            }`}>
              {scanDetails?.isBaseline ? 'Yes' : 'No'}
            </span>
          </span>
        </div>
      </header>

      <div className="mb-8 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
          Player Statistics <span className="text-sm font-normal text-gray-500 dark:text-gray-400">({scanDetails?.players?.length || 0} players)</span>
        </h2>

        {scanDetails?.players?.length ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Player Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Alliance
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Power
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Kill Points
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Dead Troops
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    T4+T5 Kills
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Resources Gathered
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {scanDetails?.players?.map((player, index) => (
                  <tr key={player.governorId || index} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {player.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {player.alliance || 'N/A'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {formatNumber(player.power)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {formatNumber(player.killPoints)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {formatNumber(player.deads)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {formatNumber(player.t45Kills)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {formatNumber(player.rssGathered)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-400'} italic`}>
            No player statistics found for this scan.
          </p>
        )}
      </div>

      {/* Alliance Summary */}
      <div className={`p-6 ${theme === 'light' ? 'bg-white' : 'bg-gray-800'} rounded-lg shadow-md border ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
        <h2 className={`text-2xl font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'} mb-4`}>
          Alliance Summary
        </h2>

        <div className="overflow-x-auto">
          <table className={`min-w-full divide-y ${theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'}`}>
            <thead className={`${theme === 'light' ? 'bg-gray-50' : 'bg-gray-900'}`}>
              <tr>
                <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                  Alliance
                </th>
                <th scope="col" className={`px-6 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                  Members
                </th>
                <th scope="col" className={`px-6 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                  Total Power
                </th>
                <th scope="col" className={`px-6 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                  Total Kill Points
                </th>
                <th scope="col" className={`px-6 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                  Total Dead Troops
                </th>
              </tr>
            </thead>
            <tbody className={`${theme === 'light' ? 'bg-white divide-y divide-gray-200' : 'bg-gray-800 divide-y divide-gray-700'}`}>
              {Object.entries(
                scanDetails?.players.reduce((acc: Record<string, { members: number; power: number; killPoints: number; deads: number }>, player) => {
                  const alliance = player.alliance || 'Unknown';
                  if (!acc[alliance]) {
                    acc[alliance] = {
                      members: 0,
                      power: 0,
                      killPoints: 0,
                      deads: 0
                    };
                  }
                  acc[alliance].members++;
                  acc[alliance].power += player.power;
                  acc[alliance].killPoints += player.killPoints;
                  acc[alliance].deads += player.deads;
                  return acc;
                }, {} as Record<string, { members: number; power: number; killPoints: number; deads: number }>)
              ).map(([alliance, stats]: [string, { members: number; power: number; killPoints: number; deads: number }]) => (
                <tr key={alliance} className={`${theme === 'light' ? 'hover:bg-gray-50' : 'hover:bg-gray-700'} transition-colors duration-150`}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm font-medium ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                      {alliance}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                      {stats.members}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                      {formatNumber(stats.power)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                      {formatNumber(stats.killPoints)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                      {formatNumber(stats.deads)}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ScanDetailPage;