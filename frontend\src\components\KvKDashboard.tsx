import React, { useState, useEffect, useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { KvKData, ScanData } from '../types/dataTypes';
import { formatLargeNumber } from '../utils/formatters';
import { useNavigate } from 'react-router-dom';

interface KvKDashboardProps {
  kvk: KvKData;
  latestScan?: ScanData;
  previousScan?: ScanData;
  baselineScan?: ScanData;
}

const KvKDashboard: React.FC<KvKDashboardProps> = ({
  kvk,
  latestScan,
  previousScan,
  baselineScan
}) => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [timeUntilUpdate, setTimeUntilUpdate] = useState<string>('00:00');
  const [lastUpdateTime, setLastUpdateTime] = useState<string>('');

  // Memoize expensive calculations
  const { totalKillPoints, killPointsGain, last24hGain } = useMemo(() => {
    const total = latestScan?.players.reduce((sum, player) => sum + player.killPoints, 0) || 0;
    const previousTotal = previousScan?.players.reduce((sum, player) => sum + player.killPoints, 0) || 0;
    const baselineTotal = baselineScan?.players.reduce((sum, player) => sum + player.killPoints, 0) || 0;

    // If we only have baseline scan, show baseline values
    if (!latestScan || latestScan.id === baselineScan?.id) {
      return {
        totalKillPoints: baselineTotal,
        killPointsGain: 0, // No gain to show yet
        last24hGain: 0
      };
    }

    return {
      totalKillPoints: total,
      killPointsGain: total - baselineTotal,
      last24hGain: total - previousTotal
    };
  }, [latestScan, previousScan, baselineScan]);

  const { t45CasualtiesGain, currentT45Kills } = useMemo(() => {
    const total = latestScan?.players.reduce((sum, player) => sum + (player.t45Kills || 0), 0) || 0;
    const baseline = baselineScan?.players.reduce((sum, player) => sum + (player.t45Kills || 0), 0) || 0;

    // If we only have baseline scan (no latest scan or they're the same), show baseline values
    if (!latestScan || latestScan.id === baselineScan?.id) {
      return {
        t45CasualtiesGain: 0, // No gain to show yet
        currentT45Kills: baseline
      };
    }

    return {
      t45CasualtiesGain: total - baseline,
      currentT45Kills: total
    };
  }, [latestScan, baselineScan]);

  const { deadsGain, currentDeads } = useMemo(() => {
    const total = latestScan?.players.reduce((sum, player) => sum + player.deads, 0) || 0;
    const baseline = baselineScan?.players.reduce((sum, player) => sum + player.deads, 0) || 0;

    // If we only have baseline scan (no latest scan or they're the same), show baseline values
    if (!latestScan || latestScan.id === baselineScan?.id) {
      return {
        deadsGain: 0, // No gain to show yet
        currentDeads: baseline
      };
    }

    return {
      deadsGain: total - baseline,
      currentDeads: total
    };
  }, [latestScan, baselineScan]);

  // Update the countdown timer
  useEffect(() => {
    if (!latestScan) return;

    // Set last update time
    const scanDate = new Date(latestScan.date);
    setLastUpdateTime(scanDate.toLocaleString());

    // Calculate next update time (hourly)
    const nextUpdate = new Date(scanDate);
    nextUpdate.setHours(nextUpdate.getHours() + 1);

    const updateTimer = () => {
      const now = new Date();
      const diff = nextUpdate.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeUntilUpdate('00:00');
        return;
      }

      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setTimeUntilUpdate(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
    };

    updateTimer();
    const timerId = setInterval(updateTimer, 1000);

    return () => clearInterval(timerId);
  }, [latestScan]);

  const handleCardClick = () => {
    if (kvk && kvk.id) {
      navigate(`/kvk/${kvk.id}`);
    }
  };

  return (
    <div
      className={`rounded-xl overflow-hidden shadow-lg transition-all duration-300 ease-in-out ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:shadow-2xl hover:bg-gray-750'} cursor-pointer group`}
      onClick={handleCardClick}
      style={{ transform: 'translateZ(0)' }} // Promotes to a new layer for smoother animations
    >
      {/* KvK Header */}
      <div
        className={`px-6 py-4 ${theme === 'light' ? 'bg-gradient-to-r from-blue-500 to-blue-600' : 'bg-gradient-to-r from-blue-700 to-blue-800'} text-white transition-colors duration-300 group-hover:from-blue-600 group-hover:to-blue-700`}
      >
        <h3 className="font-bold text-xl mb-1">{kvk.name} (Season {kvk.season})</h3>
        <p className="text-sm opacity-90">{kvk.status.charAt(0).toUpperCase() + kvk.status.slice(1)}</p>
      </div>

      {/* Stats Body */}
      <div className={`p-6 ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
        {/* Kill Points */}
        <div className={`mb-4 pb-4 border-b ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
          <div className="flex justify-between items-baseline mb-1">
            <span className="font-semibold text-md">
              {latestScan && baselineScan && latestScan.id !== baselineScan.id
                ? 'Kill Points Gain'
                : 'Total Kill Points'
              }
            </span>
            {latestScan && baselineScan && latestScan.id !== baselineScan.id && (
              <span className={`text-xs px-2 py-1 rounded-full ${theme === 'light' ? 'bg-red-100 text-red-700' : 'bg-red-700 text-red-100'}`}>
                24h: {last24hGain > 0 ? '+' : ''}{formatLargeNumber(last24hGain)}
              </span>
            )}
          </div>
          <div className={`text-3xl font-bold ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`}>
            {latestScan && baselineScan && latestScan.id !== baselineScan.id
              ? `+${formatLargeNumber(killPointsGain)}`
              : formatLargeNumber(totalKillPoints)
            }
          </div>
          <div className={`text-xs opacity-70 ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
            {latestScan && baselineScan && latestScan.id !== baselineScan.id
              ? `~(${formatLargeNumber(killPointsGain, true)})`
              : 'Baseline scan data'
            }
          </div>
        </div>

        {/* T4-5 Kills & Deads */}
        <div className={`grid grid-cols-2 gap-x-6 mb-4 pb-4 border-b ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
          <div>
            <span className="font-semibold text-md block mb-1">T4-5 Kills</span>
            <div className={`text-2xl font-bold ${theme === 'light' ? 'text-purple-600' : 'text-purple-400'}`}>
              {latestScan && baselineScan && latestScan.id !== baselineScan.id
                ? `+${formatLargeNumber(t45CasualtiesGain)}`
                : formatLargeNumber(currentT45Kills)
              }
            </div>
            <div className={`text-xs opacity-70 ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              {latestScan && baselineScan && latestScan.id !== baselineScan.id
                ? 'Gain from Baseline'
                : 'Current Total'
              }
            </div>
          </div>
          <div>
            <span className="font-semibold text-md block mb-1">Deads</span>
            <div className={`text-2xl font-bold ${theme === 'light' ? 'text-yellow-600' : 'text-yellow-400'}`}>
              {latestScan && baselineScan && latestScan.id !== baselineScan.id
                ? `+${formatLargeNumber(deadsGain)}`
                : formatLargeNumber(currentDeads)
              }
            </div>
            <div className={`text-xs opacity-70 ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              {latestScan && baselineScan && latestScan.id !== baselineScan.id
                ? 'Gain from Baseline'
                : 'Current Total'
              }
            </div>
          </div>
        </div>

        {/* Update Timer */}
        <div className={`p-4 rounded-lg ${theme === 'light' ? 'bg-green-50' : 'bg-gray-700'} relative`}>
          <div className="flex justify-between items-center">
            <div>
              <span className={`font-semibold text-md block ${theme === 'light' ? 'text-green-700' : 'text-green-300'}`}>Next Update In</span>
              <div className={`text-3xl font-bold ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`}>
                {timeUntilUpdate}
              </div>
            </div>
            <span className={`absolute top-3 right-3 text-xs px-2 py-0.5 rounded-full font-medium ${theme === 'light' ? 'bg-green-500 text-white' : 'bg-green-600 text-green-100'}`}>
              LIVE
            </span>
          </div>
          <div className={`text-xs mt-1 opacity-70 ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
            Last: {lastUpdateTime || 'N/A'} (Auto-updates hourly)
          </div>
        </div>
      </div>
    </div>
  );
};

export default KvKDashboard;
