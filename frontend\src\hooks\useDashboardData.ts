import { useQuery } from '@tanstack/react-query';
import React, { useMemo, useCallback } from 'react';
import { getKvKList } from '../services/kvkService';
import { KvKData, ScanData, PlayerScanData } from '../types/dataTypes';
import { fetchScans } from '../services/scanService';

export interface KvKScanData {
  latestScan?: ScanData;
  previousScan?: ScanData;
  baselineScan?: ScanData;
}

export const useDashboardData = () => {
  const { data: kvkList = [], isLoading: isLoadingKvKList, error: kvkListError } = useQuery<KvKData[], Error>({
    queryKey: ['kvkList'],
    queryFn: getKvKList,
  });

  const { data: scans = [], isLoading: isLoadingScans, error: scansError } = useQuery<ScanData[], Error>({
    queryKey: ['scansList'],
    queryFn: fetchScans,
  });

  const activeKvK = useMemo(() => {
    return kvkList.find(kvk => kvk.status === 'active') ||
           kvkList.find(kvk => kvk.status === 'upcoming') ||
           kvkList[0]; // Fallback to the first KvK if no active/upcoming
  }, [kvkList]);

  const getKvKScans = useCallback((kvkId: string | undefined): KvKScanData => {
    if (!kvkId || !scans || scans.length === 0) return {};
    const kvkScans = scans.filter(scan => scan.kvkId === kvkId);
    kvkScans.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    const latestScan = kvkScans[0];
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    const previousScan = kvkScans.find(scan => new Date(scan.date) <= oneDayAgo) || kvkScans[1];
    const baselineScan = kvkScans.find(scan => scan.isBaseline);

    return { latestScan, previousScan, baselineScan };
  }, [scans]);

  const recentCompletedKvKs = useMemo(() => {
    if (!kvkList || !activeKvK) return [];
    return kvkList
      .filter(kvk => kvk.id !== activeKvK?.id && kvk.status === 'completed')
      .sort((a, b) => new Date(b.endDate || 0).getTime() - new Date(a.endDate || 0).getTime());
  }, [kvkList, activeKvK]);

  // Calculate top performers from real scan data
  const topPerformers = useMemo(() => {
    if (!scans || scans.length === 0) return {
      topPowerPlayers: [],
      topKillsPlayers: [],
      topT45KillsPlayers: [],
      topDeadsPlayers: []
    };

    // Get the latest scan with player data
    const latestScanWithPlayers = scans
      .filter(scan => scan.players && scan.players.length > 0)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

    if (!latestScanWithPlayers || !latestScanWithPlayers.players) {
      return {
        topPowerPlayers: [],
        topKillsPlayers: [],
        topT45KillsPlayers: [],
        topDeadsPlayers: []
      };
    }

    const players = [...latestScanWithPlayers.players];

    return {
      topPowerPlayers: players.sort((a, b) => b.power - a.power).slice(0, 10),
      topKillsPlayers: players.sort((a, b) => b.killPoints - a.killPoints).slice(0, 10),
      topT45KillsPlayers: players.sort((a, b) => b.t45Kills - a.t45Kills).slice(0, 10),
      topDeadsPlayers: players.sort((a, b) => b.deads - a.deads).slice(0, 10)
    };
  }, [scans]);

  const topPowerGainer = topPerformers.topPowerPlayers[0] || null;
  const topKillsGainer = topPerformers.topKillsPlayers[0] || null;
  const topT45KillsGainer = topPerformers.topT45KillsPlayers[0] || null;
  const topDeadsGainer = topPerformers.topDeadsPlayers[0] || null;

  return {
    kvkList,
    isLoadingKvKList,
    kvkListError,
    scans,
    isLoadingScans,
    scansError,
    activeKvK,
    getKvKScans,
    recentCompletedKvKs,
    topPowerGainer,
    topKillsGainer,
    topT45KillsGainer,
    topDeadsGainer,
    topPowerPlayers: topPerformers.topPowerPlayers,
    topKillsPlayers: topPerformers.topKillsPlayers,
    topDeadsPlayers: topPerformers.topDeadsPlayers,
    topT45KillsPlayers: topPerformers.topT45KillsPlayers,
  };
};
