import React from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';

const UnauthorizedPage: React.FC = () => {
  const { theme } = useTheme();

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className={`max-w-md w-full space-y-8 p-10 rounded-lg shadow-md ${
        theme === 'light' ? 'bg-white' : 'bg-gray-800'
      }`}>
        <div>
          <h2 className={`mt-6 text-center text-3xl font-extrabold ${
            theme === 'light' ? 'text-gray-900' : 'text-white'
          }`}>
            Access Denied
          </h2>
          <div className="mt-8 text-center">
            <svg 
              className={`mx-auto h-16 w-16 ${theme === 'light' ? 'text-red-500' : 'text-red-400'}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" 
              />
            </svg>
          </div>
          <p className={`mt-4 text-center text-lg ${
            theme === 'light' ? 'text-gray-600' : 'text-gray-400'
          }`}>
            You don't have permission to access this page.
          </p>
        </div>
        
        <div className="mt-8 flex justify-center">
          <Link
            to="/"
            className={`inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white ${
              theme === 'light' 
                ? 'bg-blue-600 hover:bg-blue-700' 
                : 'bg-blue-500 hover:bg-blue-600'
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
          >
            Return to Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
};

export default UnauthorizedPage;
