from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional, Dict

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import crud, schemas, services, models
from database import get_db

router = APIRouter(
    prefix="/reports",
    tags=["Reports"],
    responses={404: {"description": "Not found"}},
)

@router.get("/full_summary", response_model=Dict) # Using Dict for flexibility, can be a Pydantic model too
def get_full_summary_report(
    current_scan_id: Optional[int] = None, # If None, latest scan is used
    baseline_scan_id: Optional[int] = None, # If None, default baseline is used
    db: Session = Depends(get_db)
):
    """
    Generates a comprehensive report including multiple leaderboards and summaries.
    - Uses the latest scan as `current_scan_id` if not provided.
    - Uses the default baseline scan if `baseline_scan_id` is not provided.
    """
    try:
        report_data = services.get_full_report_data(
            db=db, 
            current_scan_id=current_scan_id, 
            baseline_scan_id=baseline_scan_id
        )
        return report_data
    except HTTPException as e:
        raise e
    except Exception as e:
        # Log the error for debugging
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred while generating the report: {str(e)}")

@router.get("/leaderboard/top_kp_gain", response_model=List[schemas.PlayerPerformanceSummary])
def get_top_kp_gain_leaderboard(
    limit: int = 20,
    current_scan_id: Optional[int] = None,
    baseline_scan_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """Leaderboard for Top Kill Points Gain."""
    report_data = services.get_full_report_data(db, current_scan_id, baseline_scan_id)
    return sorted(report_data.get("all_player_summaries", []), key=lambda x: x.kp_gain, reverse=True)[:limit]

@router.get("/leaderboard/top_dead_gain", response_model=List[schemas.PlayerPerformanceSummary])
def get_top_dead_gain_leaderboard(
    limit: int = 20,
    current_scan_id: Optional[int] = None,
    baseline_scan_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """Leaderboard for Top Dead Troops Gain."""
    report_data = services.get_full_report_data(db, current_scan_id, baseline_scan_id)
    return sorted(report_data.get("all_player_summaries", []), key=lambda x: x.dead_gain, reverse=True)[:limit]

@router.get("/leaderboard/top_kp_per_dead", response_model=List[schemas.PlayerPerformanceSummary])
def get_top_kp_per_dead_leaderboard(
    limit: int = 20,
    current_scan_id: Optional[int] = None,
    baseline_scan_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """Leaderboard for Top KP per Dead Troop."""
    report_data = services.get_full_report_data(db, current_scan_id, baseline_scan_id)
    return sorted(report_data.get("all_player_summaries", []), key=lambda x: (x.kp_per_dead if x.kp_per_dead is not None else -1), reverse=True)[:limit]

@router.get("/list/power_lost", response_model=List[schemas.PlayerPerformanceSummary])
def get_power_lost_list(
    limit: int = 100, # Show more for lists
    current_scan_id: Optional[int] = None,
    baseline_scan_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """List of players by Power Lost."""
    report_data = services.get_full_report_data(db, current_scan_id, baseline_scan_id)
    return sorted(report_data.get("power_lost", []), key=lambda x: x.power_loss, reverse=True)[:limit]

@router.get("/list/zeroed_players", response_model=List[schemas.PlayerPerformanceSummary])
def get_zeroed_players_list(
    limit: int = 100,
    current_scan_id: Optional[int] = None,
    baseline_scan_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """List of Zeroed Players."""
    report_data = services.get_full_report_data(db, current_scan_id, baseline_scan_id)
    return report_data.get("zeroed_players", [])[:limit]

@router.get("/kvk/performance_summary")
def get_kvk_performance_summary(
    kvk_id: int,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get comprehensive KvK performance analysis with enhanced metrics."""
    try:
        performance_data = services.get_kvk_performance_summary(db=db, kvk_id=kvk_id, limit=limit)
        return performance_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating KvK performance summary: {str(e)}")

@router.get("/list/underperformers_kp", response_model=List[schemas.PlayerPerformanceSummary])
def get_underperformers_kp_list(
    limit: int = 100,
    current_scan_id: Optional[int] = None,
    baseline_scan_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """List of players underperforming on KP target."""
    report_data = services.get_full_report_data(db, current_scan_id, baseline_scan_id)
    return report_data.get("underperformers_kp", [])[:limit]

@router.get("/list/underperformers_dead", response_model=List[schemas.PlayerPerformanceSummary])
def get_underperformers_dead_list(
    limit: int = 100,
    current_scan_id: Optional[int] = None,
    baseline_scan_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """List of players underperforming on Dead Troops target."""
    report_data = services.get_full_report_data(db, current_scan_id, baseline_scan_id)
    return report_data.get("underperformers_dead", [])[:limit]

@router.get("/list/target_achievers_kp", response_model=List[schemas.PlayerPerformanceSummary])
def get_target_achievers_kp_list(
    limit: int = 100,
    current_scan_id: Optional[int] = None,
    baseline_scan_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """List of players who achieved the KP target."""
    report_data = services.get_full_report_data(db, current_scan_id, baseline_scan_id)
    return report_data.get("target_achievers_kp", [])[:limit]

@router.get("/list/target_achievers_dead", response_model=List[schemas.PlayerPerformanceSummary])
def get_target_achievers_dead_list(
    limit: int = 100,
    current_scan_id: Optional[int] = None,
    baseline_scan_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """List of players who achieved the Dead Troops target."""
    report_data = services.get_full_report_data(db, current_scan_id, baseline_scan_id)
    return report_data.get("target_achievers_dead", [])[:limit]

# --- Parameters Endpoints ---
@router.get("/parameters/", response_model=List[schemas.Parameter])
def read_parameters(db: Session = Depends(get_db)):
    """Retrieve all configurable parameters."""
    return services.get_all_parameters(db)

@router.put("/parameters/{parameter_name}", response_model=schemas.Parameter)
def update_parameter(
    parameter_name: str, 
    parameter_update: schemas.ParameterUpdate, 
    db: Session = Depends(get_db)
):
    """
    Update the value or description of a specific parameter.
    Only provide `value` or `description` or both in the request body.
    Example: `{"value": 1000000}` or `{"description": "New desc"}`
    """
    updated_param = crud.update_parameter(db, parameter_name=parameter_name, parameter_update=parameter_update)
    if updated_param is None:
        raise HTTPException(status_code=404, detail=f"Parameter '{parameter_name}' not found.")
    return updated_param