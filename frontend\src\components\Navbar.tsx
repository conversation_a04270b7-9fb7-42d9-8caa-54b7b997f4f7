import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { Menu, X, Moon, Sun, LogIn, LogOut, User } from 'lucide-react';

const Navbar: React.FC = () => {
  const { theme, toggleTheme } = useTheme();
  const { user, logout, isAuthenticated, canUploadScans, canViewAdminPages } = useUser();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className={`${theme === 'light' ? 'bg-white' : 'bg-gray-800'} border-b ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'} fixed w-full z-10`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link to="/" className={`text-xl font-bold ${theme === 'light' ? 'text-blue-600' : 'text-blue-400'}`}>
                KINGDOM 2358
              </Link>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              <Link
                to="/"
                className={`${isActive('/')
                  ? `${theme === 'light' ? 'border-blue-500 text-gray-900' : 'border-blue-500 text-white'}`
                  : `${theme === 'light' ? 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:border-gray-600 hover:text-gray-200'}`
                } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
              >
                Dashboard
              </Link>

              {canViewAdminPages() && (
                <Link
                  to="/scans"
                  className={`${isActive('/scans')
                    ? `${theme === 'light' ? 'border-blue-500 text-gray-900' : 'border-blue-500 text-white'}`
                    : `${theme === 'light' ? 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:border-gray-600 hover:text-gray-200'}`
                  } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                >
                  Scans
                </Link>
              )}
              <Link
                to="/performance"
                className={`${isActive('/performance')
                  ? `${theme === 'light' ? 'border-blue-500 text-gray-900' : 'border-blue-500 text-white'}`
                  : `${theme === 'light' ? 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:border-gray-600 hover:text-gray-200'}`
                } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
              >
                Performance
              </Link>

              <Link
                to="/kvk-history"
                className={`${isActive('/kvk-history')
                  ? `${theme === 'light' ? 'border-blue-500 text-gray-900' : 'border-blue-500 text-white'}`
                  : `${theme === 'light' ? 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:border-gray-600 hover:text-gray-200'}`
                } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
              >
                KvK History
              </Link>
              {canUploadScans() && (
                <Link
                  to="/upload-scan"
                  className={`${isActive('/upload-scan')
                    ? `${theme === 'light' ? 'border-blue-500 text-gray-900' : 'border-blue-500 text-white'}`
                    : `${theme === 'light' ? 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:border-gray-600 hover:text-gray-200'}`
                  } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                >
                  Upload
                </Link>
              )}
            </div>
          </div>
          <div className="hidden sm:ml-6 sm:flex sm:items-center space-x-2">
            <button
              onClick={toggleTheme}
              className={`p-2 rounded-full ${theme === 'light' ? 'bg-gray-100 text-gray-500 hover:text-gray-600' : 'bg-gray-700 text-gray-300 hover:text-gray-200'}`}
            >
              {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
            </button>

            {isAuthenticated ? (
              <div className="flex items-center space-x-2">
                <div className={`text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                  <div className="flex items-center">
                    <User size={16} className="mr-1" />
                    {user?.username} ({user?.role})
                  </div>
                </div>
                <button
                  onClick={() => {
                    logout();
                    navigate('/login');
                  }}
                  className={`p-2 rounded-full ${theme === 'light' ? 'bg-gray-100 text-gray-500 hover:text-gray-600' : 'bg-gray-700 text-gray-300 hover:text-gray-200'}`}
                >
                  <LogOut size={20} />
                </button>
              </div>
            ) : (
              <Link
                to="/login"
                className={`p-2 rounded-full ${theme === 'light' ? 'bg-gray-100 text-gray-500 hover:text-gray-600' : 'bg-gray-700 text-gray-300 hover:text-gray-200'}`}
              >
                <LogIn size={20} />
              </Link>
            )}
          </div>
          <div className="-mr-2 flex items-center sm:hidden">
            <button
              onClick={toggleMenu}
              className={`inline-flex items-center justify-center p-2 rounded-md ${theme === 'light' ? 'text-gray-400 hover:text-gray-500 hover:bg-gray-100' : 'text-gray-300 hover:text-white hover:bg-gray-700'}`}
            >
              <span className="sr-only">Open main menu</span>
              {isMenuOpen ? <X className="block h-6 w-6" /> : <Menu className="block h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="sm:hidden">
          <div className={`pt-2 pb-3 space-y-1 ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
            <Link
              to="/"
              className={`${isActive('/')
                ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
              } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
            >
              Dashboard
            </Link>

            {canViewAdminPages() && (
              <Link
                to="/scans"
                className={`${isActive('/scans')
                  ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                  : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
                } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
              >
                Scans
              </Link>
            )}
            <Link
              to="/performance"
              className={`${isActive('/performance')
                ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
              } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
            >
              Performance
            </Link>

            <Link
              to="/kvk-history"
              className={`${isActive('/kvk-history')
                ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
              } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
            >
              KvK History
            </Link>
            {canUploadScans() && (
              <Link
                to="/upload-scan"
                className={`${isActive('/upload-scan')
                  ? `${theme === 'light' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'bg-gray-700 border-blue-500 text-white'}`
                  : `${theme === 'light' ? 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' : 'border-transparent text-gray-300 hover:bg-gray-700 hover:border-gray-600 hover:text-white'}`
                } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}
              >
                Upload
              </Link>
            )}
          </div>
          <div className={`pt-4 pb-3 border-t ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
            <div className="flex items-center px-4">
              <button
                onClick={toggleTheme}
                className={`p-2 rounded-full ${theme === 'light' ? 'bg-gray-100 text-gray-500 hover:text-gray-600' : 'bg-gray-700 text-gray-300 hover:text-gray-200'}`}
              >
                {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
              </button>
              <div className="ml-3">
                <div className={`text-base font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>Theme</div>
              </div>
            </div>

            {/* Mobile auth buttons */}
            <div className="mt-3 space-y-1 px-2">
              {isAuthenticated ? (
                <>
                  <div className={`px-4 py-2 text-base font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                    <div className="flex items-center">
                      <User size={16} className="mr-2" />
                      {user?.username} ({user?.role})
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      logout();
                      navigate('/login');
                      setIsMenuOpen(false);
                    }}
                    className={`flex items-center w-full px-4 py-2 text-base font-medium ${
                      theme === 'light'
                        ? 'text-gray-500 hover:text-gray-800 hover:bg-gray-100'
                        : 'text-gray-300 hover:text-white hover:bg-gray-700'
                    } rounded-md`}
                  >
                    <LogOut size={16} className="mr-2" />
                    Sign out
                  </button>
                </>
              ) : (
                <Link
                  to="/login"
                  onClick={() => setIsMenuOpen(false)}
                  className={`flex items-center px-4 py-2 text-base font-medium ${
                    theme === 'light'
                      ? 'text-gray-500 hover:text-gray-800 hover:bg-gray-100'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                  } rounded-md`}
                >
                  <LogIn size={16} className="mr-2" />
                  Sign in
                </Link>
              )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
