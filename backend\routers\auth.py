from fastapi import API<PERSON>outer, Depends, HTTPException, status, Response
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import timedelta, timezone

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import crud, models, schemas, auth
from csrf import generate_csrf_token, CSRF_COOKIE_NAME, CSRFProtect # Import CSRF utilities
from database import get_db

router = APIRouter(tags=["Authentication"])

@router.post("/token", response_model=schemas.User) # Return User info instead of Token
async def login_for_access_token(
    response: Response, # Inject Response object to set cookies
    db: Session = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
):
    """
    OAuth2-compatible token login, get an access token for future requests.
    """
    user = crud.get_user_by_username(db, username=form_data.username)
    if not user or not auth.verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")

    access_token_expires = timedelta(minutes=auth.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = auth.create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Set the JWT as an httpOnly cookie
    response.set_cookie(
        key="access_token",
        value=access_token,
        httponly=True,
        max_age=int(access_token_expires.total_seconds()), # in seconds
        expires=access_token_expires, # also set expires for older browsers
        samesite="lax",  # Can be "lax" or "strict"
        secure=True,     # Should be True in production (requires HTTPS)
        path="/",        # Cookie available for all paths
    )

    # Generate and set CSRF token cookie
    csrf_token = generate_csrf_token()
    response.set_cookie(
        key=CSRF_COOKIE_NAME,
        value=csrf_token,
        max_age=int(access_token_expires.total_seconds()), # Same expiration as access token
        expires=access_token_expires,
        samesite="lax",
        secure=True, # Should be True in production
        path="/",
        httponly=False # IMPORTANT: Frontend JavaScript needs to read this
    )
    return user # Return user information

@router.post("/users/", response_model=schemas.User, status_code=status.HTTP_201_CREATED)
async def create_user(
    user: schemas.UserCreate,
    db: Session = Depends(get_db),
    # Apply CSRF protection. User creation is a state-changing operation.
    # Although it's often public, applying CSRF adds a layer of protection if a user
    # is somehow already authenticated (e.g. admin creating users) or to prevent
    # automated submissions by malicious sites if there's any session interaction.
    _csrf_protection: None = CSRFProtect,
):
    """
    Create a new user.
    """
    db_user_by_username = crud.get_user_by_username(db, username=user.username)
    if db_user_by_username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered",
        )
    if user.email: # Optional email
        db_user_by_email = crud.get_user_by_email(db, email=user.email)
        if db_user_by_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered",
            )

    hashed_password = auth.get_password_hash(user.password)
    user_create_data = schemas.UserCreate(
        username=user.username,
        email=user.email, # Pass email if provided
        password=hashed_password # This will be used to set hashed_password in crud
    )
    # The actual UserCreate in crud should expect the hashed password or be adapted.
    # For now, we pass the hashed password in the 'password' field of UserCreate,
    # and crud.create_user needs to handle this.
    # A more robust approach is to have a UserCreateInternal schema or similar.

    # We need a new crud function `create_user` that takes `schemas.UserCreate`
    # but uses the password field as the already hashed password.

    created_user = crud.create_user(db=db, user_create=user_create_data) # This assumes crud.create_user handles it
    return created_user

@router.post("/logout")
async def logout(response: Response):
    """
    Clears the access_token cookie to log the user out.
    """
    response.delete_cookie(
        key="access_token",
        httponly=True, # Match settings used when setting the cookie
        secure=True,   # Match settings
        samesite="lax",# Match settings
        path="/",      # Match settings
    )
    response.delete_cookie(
        key=CSRF_COOKIE_NAME, # Also clear CSRF token cookie
        httponly=False, # Match settings
        secure=True,    # Match settings
        samesite="lax", # Match settings
        path="/",       # Match settings
    )
    return {"message": "Logout successful"}
