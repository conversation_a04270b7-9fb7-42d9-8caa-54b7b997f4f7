import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Union, Any
from dotenv import load_dotenv

from jose import JW<PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel
from fastapi import Depends, HTTPException, status, Request # Added Request
from fastapi.security import OAuth2PasswordBearer # Will be replaced by a cookie-based dependency

from .database import get_db
from sqlalchemy.orm import Session
from . import models # Assuming models.py will have the User model

# Load environment variables from .env file
load_dotenv()

# Environment variables
# It's crucial to use a strong, randomly generated key for SECRET_KEY in production.
# The default value here is only for development if .env is not set up.
SECRET_KEY = os.getenv("SECRET_KEY", "fallback-secret-key-if-not-in-env")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", 30))

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
# oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token") # No longer using this for cookie auth directly

class TokenData(BaseModel):
    username: Optional[str] = None

# New dependency to get token from cookies
async def get_token_from_cookie(request: Request) -> Optional[str]:
    return request.cookies.get("access_token")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verifies a plain password against a hashed password."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hashes a password."""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Creates a new JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(
    token: Optional[str] = Depends(get_token_from_cookie), db: Session = Depends(get_db)
) -> models.User:
    """
    Decodes the token, validates the user, and returns the user object.
    Raises HTTPException if token is invalid or user not found.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: Optional[str] = payload.get("sub") # "sub" is standard for subject (username)
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    
    # Import crud here to avoid circular dependency if crud imports auth
    from . import crud  
    user = crud.get_user_by_username(db, username=token_data.username) 
    if user is None:
        raise credentials_exception # Token was valid, but user doesn't exist
    if not user.is_active: 
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user")
    return user

async def get_current_active_user(current_user: models.User = Depends(get_current_user)) -> models.User:
    """
    Same as get_current_user, but an additional check for active status
    (already handled in get_current_user in this example, but can be explicit).
    This is useful if you want to separate the concerns or have different levels of "current user".
    """
    # The active check is already in get_current_user, but if it weren't:
    # if not current_user.is_active:
    #     raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# Placeholder for a function to get user by username from DB (to be added in crud.py)
# def get_user_by_username_placeholder(db: Session, username: str) -> Optional[models.User]:
#     # This will be replaced by crud.get_user_by_username
#     # For now, let's assume it might return a dummy user or None
#     # This is just to allow auth.py to be mostly complete before crud.py is updated.
#     print(f"Attempting to fetch user: {username} (using placeholder)")
#     return None
