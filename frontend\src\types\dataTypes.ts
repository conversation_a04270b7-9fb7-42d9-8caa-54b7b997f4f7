// Core data types for the Rise of Kingdoms KvK Performance Tracker

// Player data from a scan
export interface PlayerScanData {
  // Player identifying information
  governorId: string; // From Excel 'ID' column - fixed identifier for players
  name: string;       // From Excel 'Name' column - can change, always fetch by ID
  alliance?: string;  // From Excel 'Alliance' column

  // Core stats from scan
  power: number;      // From Excel 'Power' column
  killPoints: number; // From Excel 'KillPoints' column
  deads: number;      // From Excel 'Deads' column
  t1Kills: number;    // From Excel 'T1 Kills' column
  t2Kills: number;    // From Excel 'T2 Kills' column
  t3Kills: number;    // From Excel 'T3 Kills' column
  t4Kills: number;    // From Excel 'T4 Kills' column
  t5Kills: number;    // From Excel 'T5 Kills' column
  totalKills: number; // From Excel 'Total Kills' column
  t45Kills: number;   // From Excel 'T4-5 Kills' column
  ranged: number;     // From Excel 'Ranged' column
  rssGathered: number;// From Excel 'Rss Gathered' column
  rssAssisted: number;// From Excel 'Rss Assisted' column
  helps: number;      // From Excel 'Helps' column

  // Optional/additional data that might be part of the system
  statId?: number;    // Corresponds to PlayerStat.id (the ID of the stat record itself)
  scanId?: number;    // Corresponds to PlayerStat.scan_id
  timestamp?: string | Date; // Corresponds to PlayerStat.timestamp

  allianceHonor?: number;
  vip?: number;
  killRanking?: number;
  powerRanking?: number;
  deadsRanking?: number;
  contributionRanking?: number;
}

// KvK data
export type KvKStatus = 'active' | 'completed' | 'upcoming';

export interface KvKData {
  id: string;
  name: string;
  startDate: string;
  endDate?: string | null;
  status: KvKStatus;
  season: number;
  scans?: string[]; // Array of scan IDs associated with this KvK
  baselineScanId?: string; // ID of the baseline scan for this KvK
  createdAt?: string;
  updatedAt?: string;
}

// Scan metadata
export interface ScanData {
  id: string;
  name: string;
  date: string;
  isBaseline: boolean;
  kvkId: string | null; // Reference to the KvK this scan belongs to (nullable)
  kvkPhase: KvKPhase;
  players: PlayerScanData[];
}

// KvK phases
export enum KvKPhase {
  PreKvK = "Pre-KvK",
  Preparation = "Preparation",
  LightVsDarkness = "Light vs Darkness",
  KingdomVsKingdom = "Kingdom vs Kingdom",
  FinalShowdown = "Final Showdown",
  PostKvK = "Post-KvK"
}

// Performance thresholds for different metrics
export interface PerformanceThresholds {
  power: {
    min: number;
    target: number;
    excellent: number;
  };
  killPoints: {
    min: number;
    target: number;
    excellent: number;
  };
  deadTroops: {
    min: number;
    target: number;
    excellent: number;
  };
  t4t5Kills: {
    min: number;
    target: number;
    excellent: number;
  };
  resourceAssistance: {
    min: number;
    target: number;
    excellent: number;
  };
  helpCount: {
    min: number;
    target: number;
    excellent: number;
  };
  // Zeroed detection thresholds
  zeroedPowerDropAbs: number; // Absolute power drop threshold
  zeroedPowerDropPct: number; // Percentage power drop threshold
  significantDeadForZeroed: number; // Minimum dead troops to consider zeroed
  // KP per dead ratio thresholds
  kpPerDeadMin: number;
  kpPerDeadTarget: number;
  kpPerDeadExcellent: number;
}

// Performance metrics calculated between two scans
export interface PerformanceMetrics {
  playerId: string;
  playerName: string;
  alliance: string;
  powerGrowth: number;
  killPointsGrowth: number;
  killsGrowth: {
    t1: number;
    t2: number;
    t3: number;
    t4: number;
    t5: number;
    total: number;
    t45: number; // Combined T4+T5 kills
  };
  deadsGrowth: number;
  rssGatheredGrowth: number;
  rssAssistedGrowth: number;
  helpsGrowth: number;
  allianceHonorGrowth: number;

  // KP per dead ratio
  kpPerDead: number;

  // Zeroed status
  isZeroed: boolean;

  // Performance scores (0-100)
  scores: {
    powerScore: number;
    killsScore: number;
    t45KillsScore: number;
    deadsScore: number;
    assistanceScore: number;
    activityScore: number;
    overallScore: number;
    kpPerDeadScore?: number; // Optional KP per dead score
  };

  // Performance grades (S, A, B, C, D, F)
  grades: {
    powerGrade: string;
    killsGrade: string;
    t45KillsGrade: string;
    deadsGrade: string;
    assistanceGrade: string;
    activityGrade: string;
    overallGrade: string;
    kpPerDeadGrade?: string; // Optional KP per dead grade
  };

  // Underperformance flags
  isUnderperforming: boolean;
  underperformanceReasons: string[];
}

// Alliance performance summary
export interface AlliancePerformance {
  name: string;
  memberCount: number;
  averagePower: number;
  totalPower: number;
  averageKillPoints: number;
  totalKillPoints: number;
  averageDeads: number;
  totalDeads: number;
  averageT45Kills: number;
  totalT45Kills: number;
  averageScore: number;
  underperformingCount: number;
  underperformingPercentage: number;
  topPerformers: string[]; // Player IDs
  underperformers: string[]; // Player IDs
}

// Kingdom performance summary
export interface KingdomPerformance {
  totalPlayers: number;
  activePlayers: number;
  totalPower: number;
  totalKillPoints: number;
  totalDeads: number;
  totalT45Kills: number;
  averageScore: number;
  gradeDistribution: {
    S: number;
    A: number;
    B: number;
    C: number;
    D: number;
    F: number;
  };
  alliancePerformance: AlliancePerformance[];
}

// Helper function to calculate grade from score
export function getGradeFromScore(score: number): string {
  if (score >= 90) return 'S';
  if (score >= 80) return 'A';
  if (score >= 70) return 'B';
  if (score >= 60) return 'C';
  if (score >= 50) return 'D';
  return 'F';
}

// Default performance thresholds
export const defaultThresholds: PerformanceThresholds = {
  power: {
    min: 1000000,    // 1M power growth
    target: 3000000,  // 3M power growth
    excellent: 5000000 // 5M power growth
  },
  killPoints: {
    min: 2000000,    // 2M kill points
    target: 5000000,  // 5M kill points
    excellent: 10000000 // 10M kill points
  },
  deadTroops: {
    min: 200000,     // 200K dead troops
    target: 500000,   // 500K dead troops
    excellent: 1000000 // 1M dead troops
  },
  t4t5Kills: {
    min: 1000000,    // 1M T4/T5 kills
    target: 3000000,  // 3M T4/T5 kills
    excellent: 6000000 // 6M T4/T5 kills
  },
  resourceAssistance: {
    min: 5000000,    // 5M resource assistance
    target: 15000000, // 15M resource assistance
    excellent: 30000000 // 30M resource assistance
  },
  helpCount: {
    min: 500,        // 500 helps
    target: 1000,     // 1000 helps
    excellent: 2000   // 2000 helps
  },
  // Zeroed detection thresholds
  zeroedPowerDropAbs: 5000000,    // 5M absolute power drop
  zeroedPowerDropPct: 0.2,        // 20% power drop
  significantDeadForZeroed: 100000, // 100K dead troops
  // KP per dead ratio thresholds
  kpPerDeadMin: 5,      // 5 KP per dead
  kpPerDeadTarget: 10,  // 10 KP per dead
  kpPerDeadExcellent: 15 // 15 KP per dead
};
