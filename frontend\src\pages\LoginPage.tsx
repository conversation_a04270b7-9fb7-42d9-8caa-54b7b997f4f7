import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import {
  FaShieldAlt,
  FaUser,
  FaLock,
  FaSignInAlt,
  FaCrown,
  FaExclamationTriangle,
  FaEye,
  FaEyeSlash
} from 'react-icons/fa';

const LoginPage: React.FC = () => {
  const { theme } = useTheme();
  const { login } = useUser();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the redirect path from the location state or default to dashboard
  const from = location.state?.from?.pathname || '/';

  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      const success = await login(username, password);
      if (success) {
        // Redirect to the page they were trying to access or dashboard
        navigate(from, { replace: true });
      } else {
        setError('Invalid username or password');
      }
    } catch (err) {
      setError('An error occurred during login. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${
      theme === 'light'
        ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50'
        : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full opacity-20 ${
          theme === 'light' ? 'bg-blue-300' : 'bg-blue-600'
        }`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full opacity-20 ${
          theme === 'light' ? 'bg-indigo-300' : 'bg-indigo-600'
        }`}></div>
      </div>

      <div className="relative z-10 max-w-md w-full">
        {/* Enhanced Header Card */}
        <div className={`rounded-3xl shadow-2xl overflow-hidden ${
          theme === 'light' ? 'bg-white/90 backdrop-blur-sm' : 'bg-gray-800/90 backdrop-blur-sm'
        }`}>
          {/* Kingdom Header */}
          <div className={`px-8 py-12 text-center ${
            theme === 'light'
              ? 'bg-gradient-to-r from-blue-600 to-indigo-600'
              : 'bg-gradient-to-r from-blue-700 to-indigo-700'
          }`}>
            <div className="flex justify-center mb-4">
              <div className="p-4 rounded-full bg-white/20 backdrop-blur-sm">
                <FaCrown className="text-4xl text-white" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Kingdom 2358
            </h1>
            <p className="text-blue-100 text-lg">
              Asahikawa Data Portal
            </p>
            <div className="flex items-center justify-center mt-4">
              <FaShieldAlt className="text-white/80 mr-2" />
              <span className="text-white/80 text-sm font-medium">Secure Access</span>
            </div>
          </div>

          {/* Login Form */}
          <div className="px-8 py-8">
            <div className="text-center mb-8">
              <h2 className={`text-2xl font-bold ${
                theme === 'light' ? 'text-gray-900' : 'text-white'
              }`}>
                Welcome Back
              </h2>
              <p className={`mt-2 text-sm ${
                theme === 'light' ? 'text-gray-600' : 'text-gray-400'
              }`}>
                Sign in to access your kingdom's data
              </p>
            </div>

            {error && (
              <div className={`mb-6 p-4 rounded-xl border-l-4 ${
                theme === 'light'
                  ? 'bg-red-50 border-red-400 text-red-700'
                  : 'bg-red-900/20 border-red-500 text-red-400'
              }`}>
                <div className="flex items-center">
                  <FaExclamationTriangle className="mr-3 text-lg" />
                  <span className="font-medium">{error}</span>
                </div>
              </div>
            )}

            <form className="space-y-6" onSubmit={handleSubmit}>
              {/* Username Field */}
              <div className="space-y-2">
                <label htmlFor="username" className={`text-sm font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  Username
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaUser className={`text-lg ${
                      theme === 'light' ? 'text-gray-400' : 'text-gray-500'
                    }`} />
                  </div>
                  <input
                    id="username"
                    name="username"
                    type="text"
                    required
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className={`block w-full pl-12 pr-4 py-3 rounded-xl border transition-all duration-300 ${
                      theme === 'light'
                        ? 'border-gray-300 bg-gray-50 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                        : 'border-gray-600 bg-gray-700 text-white placeholder-gray-400 focus:bg-gray-600 focus:border-blue-400 focus:ring-2 focus:ring-blue-800'
                    } focus:outline-none`}
                    placeholder="Enter your username"
                  />
                </div>
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <label htmlFor="password" className={`text-sm font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaLock className={`text-lg ${
                      theme === 'light' ? 'text-gray-400' : 'text-gray-500'
                    }`} />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className={`block w-full pl-12 pr-12 py-3 rounded-xl border transition-all duration-300 ${
                      theme === 'light'
                        ? 'border-gray-300 bg-gray-50 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                        : 'border-gray-600 bg-gray-700 text-white placeholder-gray-400 focus:bg-gray-600 focus:border-blue-400 focus:ring-2 focus:ring-blue-800'
                    } focus:outline-none`}
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className={`absolute inset-y-0 right-0 pr-3 flex items-center ${
                      theme === 'light' ? 'text-gray-400 hover:text-gray-600' : 'text-gray-500 hover:text-gray-300'
                    }`}
                  >
                    {showPassword ? <FaEyeSlash className="text-lg" /> : <FaEye className="text-lg" />}
                  </button>
                </div>
              </div>

              {/* Submit Button */}
              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`group relative w-full flex justify-center items-center py-4 px-6 rounded-xl font-semibold text-white transition-all duration-300 transform ${
                    isLoading
                      ? 'bg-gray-400 cursor-not-allowed'
                      : theme === 'light'
                      ? 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 hover:scale-105 shadow-lg hover:shadow-xl'
                      : 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 hover:scale-105 shadow-lg hover:shadow-xl'
                  } focus:outline-none focus:ring-4 focus:ring-blue-300`}
                >
                  {isLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Signing in...
                    </>
                  ) : (
                    <>
                      <FaSignInAlt className="mr-3 text-lg group-hover:animate-pulse" />
                      Sign In to Kingdom
                    </>
                  )}
                </button>
              </div>

              {/* Demo Info */}
              <div className={`mt-8 p-4 rounded-xl border ${
                theme === 'light'
                  ? 'bg-blue-50 border-blue-200 text-blue-800'
                  : 'bg-blue-900/20 border-blue-700 text-blue-300'
              }`}>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <FaShieldAlt className="mr-2 text-sm" />
                    <span className="font-semibold text-sm">Demo Accounts</span>
                  </div>
                  <div className="space-y-1 text-xs">
                    <p><strong>Usernames:</strong> admin, officer, member, guest</p>
                    <p><strong>Password:</strong> password</p>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
