from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Boolean, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base

class Player(Base):
    __tablename__ = "players"

    id = Column(Integer, primary_key=True, index=True)
    governor_id = Column(String, unique=True, index=True, nullable=True)
    name = Column(String, index=True)
    alliance = Column(String, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    stats = relationship("PlayerStat", back_populates="player")
    deltas = relationship("DeltaStat", back_populates="player")

class Scan(Base):
    __tablename__ = "scans"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)  # e.g., "Pre-KVK", "Scan 1", etc.
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    is_baseline = Column(Boolean, default=False)  # Whether this is a baseline scan
    kvk_id = Column(Integer, ForeignKey("kvks.id"), nullable=True)  # Link to KvK
    kvk_phase = Column(String, nullable=True)  # Phase of KvK when scan was taken

    # Relationships
    player_stats = relationship("PlayerStat", back_populates="scan")
    kvk = relationship("KvK", back_populates="scans")

class PlayerStat(Base):
    __tablename__ = "player_stats"

    id = Column(Integer, primary_key=True, index=True)
    player_id = Column(Integer, ForeignKey("players.id"))
    scan_id = Column(Integer, ForeignKey("scans.id"))
    power = Column(BigInteger)  # Changed to BigInteger for large power values
    kill_points = Column(BigInteger)  # Total kill points from Excel
    kill_points_t1 = Column(BigInteger, default=0)  # T1 Kills from Excel
    kill_points_t2 = Column(BigInteger, default=0)  # T2 Kills from Excel
    kill_points_t3 = Column(BigInteger, default=0)  # T3 Kills from Excel
    kill_points_t4 = Column(BigInteger, default=0)  # T4 Kills from Excel
    kill_points_t5 = Column(BigInteger, default=0)  # T5 Kills from Excel
    total_kill_points = Column(BigInteger)  # Total Kills from Excel
    t45_kills = Column(BigInteger, default=0)  # T4-5 Kills from Excel
    ranged = Column(BigInteger, default=0)  # Ranged from Excel
    rss_gathered = Column(BigInteger, default=0)  # Rss Gathered from Excel
    rss_assistance = Column(BigInteger, default=0)  # Rss Assisted from Excel
    helps = Column(BigInteger, default=0)  # Helps from Excel
    dead_troops = Column(BigInteger)  # Deads from Excel
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    player = relationship("Player", back_populates="stats")
    scan = relationship("Scan", back_populates="player_stats")

class DeltaStat(Base):
    __tablename__ = "delta_stats"

    id = Column(Integer, primary_key=True, index=True)
    player_id = Column(Integer, ForeignKey("players.id"))
    start_scan_id = Column(Integer, ForeignKey("scans.id"))
    end_scan_id = Column(Integer, ForeignKey("scans.id"))
    power_delta = Column(Integer)
    kill_points_delta = Column(Integer)
    dead_troops_delta = Column(Integer)
    kp_per_dead = Column(Float)  # Performance ratio
    is_zeroed = Column(Boolean, default=False)
    meets_kp_target = Column(Boolean, default=False)
    meets_dead_target = Column(Boolean, default=False)
    is_new_player = Column(Boolean, default=False)  # Player joined kingdom during KvK
    player_left_kingdom = Column(Boolean, default=False)  # Player left kingdom during KvK
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    player = relationship("Player", back_populates="deltas")

class KvK(Base):
    __tablename__ = "kvks"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True), nullable=True)
    status = Column(String, default="upcoming")  # upcoming, active, completed
    season = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    scans = relationship("Scan", back_populates="kvk")

class Parameter(Base):
    __tablename__ = "parameters"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True)
    value = Column(Float)
    description = Column(String)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())