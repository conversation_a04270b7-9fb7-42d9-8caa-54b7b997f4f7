import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { useQuery } from '@tanstack/react-query';
import { getKvKList, getScans } from '../api/api';
import { formatLargeNumber, formatDate } from '../utils/formatters';

const KvKHistoryPage: React.FC = () => {
  const { theme } = useTheme();
  const { canManageKvK } = useUser();
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch KvK data
  const { data: kvkList = [], isLoading: isLoadingKvK } = useQuery({
    queryKey: ['kvkList'],
    queryFn: getKvKList
  });

  // Fetch all scans
  const { data: allScans = [], isLoading: isLoadingScans } = useQuery({
    queryKey: ['scansList'],
    queryFn: getScans
  });

  // Filter completed and active KvKs (for history viewing)
  const completedKvKs = kvkList.filter(kvk => kvk.status === 'completed' || kvk.status === 'active');

  // Filter KvKs based on search term
  const filteredKvKs = completedKvKs.filter(kvk =>
    kvk.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    kvk.season.toString().includes(searchTerm)
  );

  // Sort KvKs by season (newest first)
  const sortedKvKs = [...filteredKvKs].sort((a, b) => b.season - a.season);

  // Get scans for a KvK
  const getKvKScans = (kvkId: string) => {
    return allScans.filter(scan => scan.kvkId === kvkId);
  };

  // Get KvK statistics (simplified for now)
  const getKvKStats = (kvkId: string) => {
    const kvkScans = getKvKScans(kvkId);

    // For now, return basic stats since scan data doesn't include player details
    // TODO: Integrate with performance summary API for detailed stats
    return {
      totalKillPoints: kvkScans.length > 1 ? 'TBD' : 0,
      totalDeads: kvkScans.length > 1 ? 'TBD' : 0,
      totalT45Kills: kvkScans.length > 1 ? 'TBD' : 0,
      playerCount: kvkScans.length > 0 ? 'Available' : 0
    };
  };

  // Loading state
  if (isLoadingKvK || isLoadingScans) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className={`text-center ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
          Loading KvK history...
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <header className="mb-8">
        <h1 className={`text-3xl font-bold mb-2 ${theme === 'light' ? 'text-blue-600' : 'text-blue-400'}`}>
          KvK History
        </h1>
        <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
          View historical data from previous Kingdom vs Kingdom events
        </p>
      </header>

      {/* Search and Controls */}
      <div className="mb-8 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="w-full md:w-1/3">
          <input
            type="text"
            placeholder="Search KvK by name or season..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full px-4 py-2 rounded-md border ${
              theme === 'light'
                ? 'border-gray-300 bg-white text-gray-900'
                : 'border-gray-600 bg-gray-700 text-white'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          />
        </div>

        {canManageKvK() && (
          <Link
            to="/create-kvk"
            className={`px-4 py-2 rounded-md ${
              theme === 'light'
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-blue-700 text-white hover:bg-blue-600'
            }`}
          >
            Create New KvK
          </Link>
        )}
      </div>

      {/* KvK History Cards */}
      {sortedKvKs.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedKvKs.map(kvk => {
            const stats = getKvKStats(kvk.id);
            return (
              <div
                key={kvk.id}
                className={`rounded-lg shadow-md overflow-hidden ${
                  theme === 'light' ? 'bg-white' : 'bg-gray-800'
                }`}
              >
                <div className={`px-4 py-3 ${theme === 'light' ? 'bg-blue-50' : 'bg-blue-900/20'}`}>
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className={`font-semibold ${theme === 'light' ? 'text-blue-700' : 'text-blue-300'}`}>
                        {kvk.name}
                      </h3>
                      <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                        Season {kvk.season}
                      </p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      kvk.status === 'active'
                        ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                        : kvk.status === 'completed'
                        ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                    }`}>
                      {kvk.status.charAt(0).toUpperCase() + kvk.status.slice(1)}
                    </span>
                  </div>
                </div>
                <div className="p-4">
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        Start Date
                      </p>
                      <p className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {formatDate(kvk.startDate)}
                      </p>
                    </div>
                    <div>
                      <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        End Date
                      </p>
                      <p className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {kvk.endDate ? formatDate(kvk.endDate) : 'N/A'}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between">
                      <span className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        Kill Points
                      </span>
                      <span className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {typeof stats.totalKillPoints === 'number' ? formatLargeNumber(stats.totalKillPoints) : stats.totalKillPoints}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        Dead Troops
                      </span>
                      <span className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {typeof stats.totalDeads === 'number' ? formatLargeNumber(stats.totalDeads) : stats.totalDeads}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        T4-5 Kills
                      </span>
                      <span className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {typeof stats.totalT45Kills === 'number' ? formatLargeNumber(stats.totalT45Kills) : stats.totalT45Kills}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        Players
                      </span>
                      <span className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {stats.playerCount}
                      </span>
                    </div>
                  </div>

                  <Link
                    to={`/kvk/${kvk.id}`}
                    className={`block w-full text-center px-4 py-2 rounded-md ${
                      theme === 'light'
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-blue-700 text-white hover:bg-blue-600'
                    }`}
                  >
                    View Details
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className={`p-8 rounded-lg border text-center ${
          theme === 'light' ? 'bg-gray-50 border-gray-200 text-gray-500' : 'bg-gray-800 border-gray-700 text-gray-400'
        }`}>
          {searchTerm ? 'No KvKs match your search criteria.' : 'No KvKs available yet.'}
        </div>
      )}
    </div>
  );
};

export default KvKHistoryPage;
