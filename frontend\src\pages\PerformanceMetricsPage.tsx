import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import ChartCard from '../components/ChartCard';
import Leaderboard from '../components/Leaderboard';
import { useDashboardData } from '../hooks/useDashboardData';

const PerformanceMetricsPage: React.FC = () => {
  const { theme } = useTheme();
  const [showUnderperforming, setShowUnderperforming] = useState<boolean>(false);

  // Get data from dashboard hook
  const {
    scans,
    isLoadingScans,
    scansError,
    topKillsPlayers,
    topT45KillsPlayers,
    topDeadsPlayers,
    topPowerPlayers
  } = useDashboardData();

  // Format large numbers
  const formatNumber = (num: number): string => {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(1)}B`;
    }
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // Get latest scan for kingdom stats
  const latestScan = scans.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
  
  // Calculate kingdom stats from latest scan
  const kingdomStats = {
    totalKillPoints: latestScan?.players?.reduce((sum, p) => sum + p.killPoints, 0) || 0,
    totalDeads: latestScan?.players?.reduce((sum, p) => sum + p.deads, 0) || 0,
    totalT45Kills: latestScan?.players?.reduce((sum, p) => sum + p.t45Kills, 0) || 0,
    totalPlayers: latestScan?.players?.length || 0,
    activePlayers: latestScan?.players?.filter(p => p.killPoints > 0 || p.deads > 0).length || 0
  };

  // Generate grade distribution data
  const gradeDistribution = [
    { name: 'S', value: topKillsPlayers.filter(p => p.killPoints > 100000000).length },
    { name: 'A', value: topKillsPlayers.filter(p => p.killPoints > 50000000 && p.killPoints <= 100000000).length },
    { name: 'B', value: topKillsPlayers.filter(p => p.killPoints > 20000000 && p.killPoints <= 50000000).length },
    { name: 'C', value: topKillsPlayers.filter(p => p.killPoints > 10000000 && p.killPoints <= 20000000).length },
    { name: 'D', value: topKillsPlayers.filter(p => p.killPoints <= 10000000).length }
  ];

  // Generate activity trend data (mock data for now)
  const activityTrend = [
    { name: 'Mon', value: 145 },
    { name: 'Tue', value: 152 },
    { name: 'Wed', value: 148 },
    { name: 'Thu', value: 160 },
    { name: 'Fri', value: 155 },
    { name: 'Sat', value: 162 },
    { name: 'Sun', value: 158 }
  ];

  if (isLoadingScans) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading performance data...</p>
        </div>
      </div>
    );
  }

  if (scansError) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center text-red-600">
          <p>Error loading performance data</p>
          <p className="text-sm mt-2">{scansError.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`container mx-auto px-4 py-8 ${theme === 'light' ? 'bg-gray-50' : 'bg-gray-900'}`}>
      <header className="mb-8">
        <h1 className={`text-3xl font-bold mb-2 ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
          Performance Metrics
        </h1>
        <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
          Detailed analysis of kingdom and player performance
        </p>
      </header>

      {/* Kingdom Overview */}
      <section className="mb-10">
        <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
          Kingdom Overview
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className={`p-6 rounded-lg shadow-md ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
            <h3 className={`text-lg font-medium mb-2 ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
              Total Kill Points
            </h3>
            <p className="text-3xl font-bold text-blue-500">{formatNumber(kingdomStats.totalKillPoints)}</p>
            <p className={`text-sm mt-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              +{formatNumber(kingdomStats.totalKillPoints * 0.15)} in last 7 days
            </p>
          </div>
          <div className={`p-6 rounded-lg shadow-md ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
            <h3 className={`text-lg font-medium mb-2 ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
              Total Dead Troops
            </h3>
            <p className="text-3xl font-bold text-red-500">{formatNumber(kingdomStats.totalDeads)}</p>
            <p className={`text-sm mt-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              +{formatNumber(kingdomStats.totalDeads * 0.08)} in last 7 days
            </p>
          </div>
          <div className={`p-6 rounded-lg shadow-md ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
            <h3 className={`text-lg font-medium mb-2 ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
              T4-5 Kills
            </h3>
            <p className="text-3xl font-bold text-green-500">{formatNumber(kingdomStats.totalT45Kills)}</p>
            <p className={`text-sm mt-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              +{formatNumber(kingdomStats.totalT45Kills * 0.12)} in last 7 days
            </p>
          </div>
          <div className={`p-6 rounded-lg shadow-md ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
            <h3 className={`text-lg font-medium mb-2 ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
              Active Players
            </h3>
            <p className="text-3xl font-bold text-purple-500">{kingdomStats.activePlayers}/{kingdomStats.totalPlayers}</p>
            <p className={`text-sm mt-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              {kingdomStats.totalPlayers > 0 ? Math.round(kingdomStats.activePlayers / kingdomStats.totalPlayers * 100) : 0}% participation rate
            </p>
          </div>
        </div>
      </section>

      {/* Performance Charts */}
      <section className="mb-10">
        <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
          Performance Analysis
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ChartCard
            title="Player Grade Distribution"
            data={gradeDistribution}
            type="bar"
          />
          <ChartCard
            title="Daily Active Players"
            data={activityTrend}
            type="bar"
          />
        </div>
      </section>

      {/* Top Performers */}
      <section className="mb-10">
        <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
          Top Performers
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Leaderboard
            title="Top Kill Points"
            players={topKillsPlayers}
            valueKey="killPoints"
            valueFormatter={(val) => formatNumber(val)}
          />
          <Leaderboard
            title="Top T4-5 Kills"
            players={topT45KillsPlayers}
            valueKey="t45Kills"
            valueFormatter={(val) => formatNumber(val)}
          />
        </div>
      </section>

      {/* Underperforming Players */}
      <section className="mb-10">
        <div className="flex justify-between items-center mb-6">
          <h2 className={`text-2xl font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
            Player Activity Status
          </h2>
          <button
            onClick={() => setShowUnderperforming(!showUnderperforming)}
            className={`px-4 py-2 rounded-md font-medium transition-colors ${
              showUnderperforming
                ? 'bg-red-600 text-white hover:bg-red-700'
                : theme === 'light'
                ? 'bg-gray-200 text-gray-800 hover:bg-gray-300'
                : 'bg-gray-700 text-gray-200 hover:bg-gray-600'
            }`}
          >
            {showUnderperforming ? 'Hide' : 'Show'} Underperforming Players
          </button>
        </div>

        {showUnderperforming && (
          <div className={`p-6 rounded-lg shadow-md ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
            <p className={`mb-4 ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              Players with less than 1M kill points in the last 7 days
            </p>
            <div className="space-y-2">
              {topKillsPlayers
                .filter(player => player.killPoints < 1000000)
                .slice(0, 10)
                .map((player, index) => (
                  <div
                    key={player.governorId}
                    className={`flex justify-between items-center p-3 rounded ${
                      theme === 'light' ? 'bg-red-50' : 'bg-red-900/20'
                    }`}
                  >
                    <div>
                      <span className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
                        {player.name}
                      </span>
                      <span className={`ml-2 text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        [{player.alliance || 'No Alliance'}]
                      </span>
                    </div>
                    <span className="text-red-600 font-medium">
                      {formatNumber(player.killPoints)} kills
                    </span>
                  </div>
                ))}
            </div>
          </div>
        )}
      </section>
    </div>
  );
};

export default PerformanceMetricsPage;
