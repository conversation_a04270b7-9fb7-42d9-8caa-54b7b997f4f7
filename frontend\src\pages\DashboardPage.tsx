import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { Link } from 'react-router-dom';
import KvKDashboard from '../components/KvKDashboard';
import StatCard from '../components/StatCard';
import Leaderboard from '../components/Leaderboard';
import ChartCard from '../components/ChartCard';
import { useDashboardData } from '../hooks/useDashboardData';
import { FaArrowUp, FaSkullCrossbones, FaFistRaised, FaShieldAlt } from 'react-icons/fa';

const DashboardPage: React.FC = () => {
  const { theme } = useTheme();
  const { user } = useUser();
  const canUploadScans = user?.canUploadScans || false;
  const canManageKvK = user?.canManageKvK || false;

  // Use the dashboard hook to get all data
  const {
    kvkList,
    isLoadingKvKList,
    kvkListError,
    scans,
    isLoadingScans,
    scansError,
    activeKvK,
    topPowerGainer,
    topKillsGainer,
    topT45KillsGainer,
    topDeadsGainer,
    topPowerPlayers,
    topKillsPlayers,
    topDeadsPlayers,
    topT45KillsPlayers
  } = useDashboardData();

  // Format number helper
  const formatNumber = (num: number): string => {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(1)}B`;
    }
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // Get scans for active KvK
  const activeKvKScans = activeKvK ? scans.filter(scan => scan.kvkId === activeKvK.id) : [];

  // Get latest scan
  const latestScan = activeKvKScans.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

  // Get baseline scan
  const baselineScan = activeKvKScans.find(scan => scan.isBaseline);

  // Calculate kingdom stats from latest scan
  const kingdomStats = {
    totalPower: latestScan?.players?.reduce((sum, p) => sum + p.power, 0) || 0,
    totalKillPoints: latestScan?.players?.reduce((sum, p) => sum + p.killPoints, 0) || 0,
    totalDeads: latestScan?.players?.reduce((sum, p) => sum + p.deads, 0) || 0,
    totalPlayers: latestScan?.players?.length || 0,
    // Calculate active players as those with power > 0 (not zeroed)
    activePlayers: latestScan?.players?.filter(p => p.power > 0).length || 0
  };

  if (isLoadingKvKList || isLoadingScans) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  if (kvkListError || scansError) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center text-red-600">
          <p>Error loading dashboard data</p>
          <p className="text-sm mt-2">{(kvkListError || scansError)?.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`container mx-auto px-4 py-8 transition-colors duration-300 ${theme === 'light' ? 'bg-slate-100' : 'bg-gray-900'}`}>
      <header className="mb-8 flex flex-col md:flex-row justify-between items-start md:items-center">
        <h1 className={`text-4xl font-bold mb-4 md:mb-0 ${theme === 'light' ? 'text-indigo-700' : 'text-indigo-400'}`}>
          Asahikawa Performance Hub
        </h1>
        <div className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
          Last updated: {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}
        </div>
      </header>

      {/* Summary Cards */}
      <section className="mb-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Power"
          value={kingdomStats.totalPower > 0 ? kingdomStats.totalPower : 'No data'}
          icon={<FaShieldAlt className="h-6 w-6" />}
          formatter={kingdomStats.totalPower > 0 ? formatNumber : undefined}
        />
        <StatCard
          title="Kill Points"
          value={kingdomStats.totalKillPoints > 0 ? kingdomStats.totalKillPoints : 'No data'}
          icon={<FaFistRaised className="h-6 w-6" />}
          formatter={kingdomStats.totalKillPoints > 0 ? formatNumber : undefined}
        />
        <StatCard
          title="Dead Troops"
          value={kingdomStats.totalDeads > 0 ? kingdomStats.totalDeads : 'No data'}
          icon={<FaSkullCrossbones className="h-6 w-6" />}
          formatter={kingdomStats.totalDeads > 0 ? formatNumber : undefined}
        />
        <StatCard
          title="Active Players"
          value={kingdomStats.totalPlayers > 0 ? `${kingdomStats.activePlayers}/${kingdomStats.totalPlayers}` : 'No data'}
          icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" /></svg>}
        />
      </section>

      {/* Player Performance Highlights */}
      <section className="mb-10">
        <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
          Player Performance Highlights
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {topPowerGainer && (
            <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
              <div className="flex items-center mb-3">
                <FaArrowUp className={`h-8 w-8 mr-3 ${theme === 'light' ? 'text-green-500' : 'text-green-400'}`} />
                <div>
                  <h3 className={`text-lg font-semibold ${theme === 'light' ? 'text-gray-700' : 'text-gray-200'}`}>Top Power Gainer</h3>
                  <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>{topPowerGainer.name} [{topPowerGainer.alliance || 'N/A'}]</p>
                </div>
              </div>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`}>+{formatNumber(topPowerGainer.power)}</p>
            </div>
          )}

          {topKillsGainer && (
            <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
              <div className="flex items-center mb-3">
                <FaFistRaised className={`h-8 w-8 mr-3 ${theme === 'light' ? 'text-red-500' : 'text-red-400'}`} />
                <div>
                  <h3 className={`text-lg font-semibold ${theme === 'light' ? 'text-gray-700' : 'text-gray-200'}`}>Top Kills Gainer</h3>
                  <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>{topKillsGainer.name} [{topKillsGainer.alliance || 'N/A'}]</p>
                </div>
              </div>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`}>+{formatNumber(topKillsGainer.killPoints)}</p>
            </div>
          )}

          {topT45KillsGainer && (
            <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
              <div className="flex items-center mb-3">
                <FaShieldAlt className={`h-8 w-8 mr-3 ${theme === 'light' ? 'text-purple-500' : 'text-purple-400'}`} />
                <div>
                  <h3 className={`text-lg font-semibold ${theme === 'light' ? 'text-gray-700' : 'text-gray-200'}`}>Top T4/T5 Kills</h3>
                  <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>{topT45KillsGainer.name} [{topT45KillsGainer.alliance || 'N/A'}]</p>
                </div>
              </div>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-purple-600' : 'text-purple-400'}`}>+{formatNumber(topT45KillsGainer.t45Kills)}</p>
            </div>
          )}

          {topDeadsGainer && (
            <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
              <div className="flex items-center mb-3">
                <FaSkullCrossbones className={`h-8 w-8 mr-3 ${theme === 'light' ? 'text-yellow-500' : 'text-yellow-400'}`} />
                <div>
                  <h3 className={`text-lg font-semibold ${theme === 'light' ? 'text-gray-700' : 'text-gray-200'}`}>Most Deads</h3>
                  <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>{topDeadsGainer.name} [{topDeadsGainer.alliance || 'N/A'}]</p>
                </div>
              </div>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-yellow-600' : 'text-yellow-400'}`}>{formatNumber(topDeadsGainer.deads)}</p>
            </div>
          )}
        </div>
      </section>

      {/* Active KvK Dashboard */}
      {activeKvK && (
        <section className="mb-10">
          <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
            Active KvK: {activeKvK.name}
          </h2>
          <KvKDashboard
            kvk={activeKvK}
            latestScan={latestScan}
            previousScan={activeKvKScans[1]}
            baselineScan={baselineScan}
          />
        </section>
      )}

      {/* Leaderboards */}
      <section className="mb-10">
        <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
          Leaderboards
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Leaderboard
            title="Top Players by Power"
            players={topPowerPlayers}
            valueKey="power"
            valueFormatter={(val) => formatNumber(val)}
          />
          <Leaderboard
            title="Top Players by Kills"
            players={topKillsPlayers}
            valueKey="killPoints"
            valueFormatter={(val) => formatNumber(val)}
          />
          <Leaderboard
            title="Top Players by T4-5 Kills"
            players={topT45KillsPlayers}
            valueKey="t45Kills"
            valueFormatter={(val) => formatNumber(val)}
          />
          <Leaderboard
            title="Top Players by Dead Troops"
            players={topDeadsPlayers}
            valueKey="deads"
            valueFormatter={(val) => formatNumber(val)}
          />
        </div>
      </section>

      {/* Admin Actions */}
      {(canUploadScans || canManageKvK) && (
        <section className="mb-10">
          <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
            Admin Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {canUploadScans && (
              <Link
                to="/upload-scan"
                className={`p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow ${
                  theme === 'light' ? 'bg-white hover:bg-gray-50' : 'bg-gray-800 hover:bg-gray-700'
                }`}
              >
                <h3 className={`text-lg font-semibold mb-2 ${theme === 'light' ? 'text-blue-600' : 'text-blue-400'}`}>
                  Upload General Scan
                </h3>
                <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                  Upload player statistics not tied to any specific KvK
                </p>
              </Link>
            )}

            {canManageKvK && (
              <Link
                to="/create-kvk"
                className={`p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow ${
                  theme === 'light' ? 'bg-white hover:bg-gray-50' : 'bg-gray-800 hover:bg-gray-700'
                }`}
              >
                <h3 className={`text-lg font-semibold mb-2 ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`}>
                  Create KvK
                </h3>
                <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                  Set up a new Kingdom vs Kingdom event
                </p>
              </Link>
            )}

            <Link
              to="/scans"
              className={`p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow ${
                theme === 'light' ? 'bg-white hover:bg-gray-50' : 'bg-gray-800 hover:bg-gray-700'
              }`}
            >
              <h3 className={`text-lg font-semibold mb-2 ${theme === 'light' ? 'text-purple-600' : 'text-purple-400'}`}>
                View All Scans
              </h3>
              <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                Browse and manage all uploaded scan data
              </p>
            </Link>
          </div>
        </section>
      )}
    </div>
  );
};

export default DashboardPage;
