import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { Link } from 'react-router-dom';
import KvKDashboard from '../components/KvKDashboard';
import StatCard from '../components/StatCard';
import Leaderboard from '../components/Leaderboard';
import ChartCard from '../components/ChartCard';
import { useDashboardData } from '../hooks/useDashboardData';
import {
  FaArrowUp,
  FaSkullCrossbones,
  FaFistRaised,
  FaShieldAlt,
  FaCrown,
  FaTrophy,
  FaChartLine,
  FaUsers,
  FaSwords,
  FaUpload,
  FaPlus
} from 'react-icons/fa';

const DashboardPage: React.FC = () => {
  const { theme } = useTheme();
  const { user } = useUser();
  const canUploadScans = user?.canUploadScans || false;
  const canManageKvK = user?.canManageKvK || false;

  // Use the dashboard hook to get all data
  const {
    kvkList,
    isLoadingKvKList,
    kvkListError,
    scans,
    isLoadingScans,
    scansError,
    activeKvK,
    topPowerGainer,
    topKillsGainer,
    topT45KillsGainer,
    topDeadsGainer,
    topPowerPlayers,
    topKillsPlayers,
    topDeadsPlayers,
    topT45KillsPlayers
  } = useDashboardData();

  // Format number helper
  const formatNumber = (num: number): string => {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(1)}B`;
    }
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // Get scans for active KvK
  const activeKvKScans = activeKvK ? scans.filter(scan => scan.kvkId === activeKvK.id) : [];

  // Get latest scan
  const latestScan = activeKvKScans.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

  // Get baseline scan
  const baselineScan = activeKvKScans.find(scan => scan.isBaseline);

  // Calculate kingdom stats from latest scan
  const kingdomStats = {
    totalPower: latestScan?.players?.reduce((sum, p) => sum + p.power, 0) || 0,
    totalKillPoints: latestScan?.players?.reduce((sum, p) => sum + p.killPoints, 0) || 0,
    totalDeads: latestScan?.players?.reduce((sum, p) => sum + p.deads, 0) || 0,
    totalPlayers: latestScan?.players?.length || 0,
    // Calculate active players as those with power > 0 (not zeroed)
    activePlayers: latestScan?.players?.filter(p => p.power > 0).length || 0
  };

  if (isLoadingKvKList || isLoadingScans) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  if (kvkListError || scansError) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center text-red-600">
          <p>Error loading dashboard data</p>
          <p className="text-sm mt-2">{(kvkListError || scansError)?.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Enhanced Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600'
            : 'bg-gradient-to-r from-blue-800 via-indigo-800 to-purple-800'
        } shadow-2xl`}>
          <div className="relative px-8 py-12">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div className="flex items-center mb-6 md:mb-0">
                <div className="p-4 rounded-2xl bg-white/20 backdrop-blur-sm mr-6">
                  <FaShieldAlt className="text-4xl text-white" />
                </div>
                <div>
                  <h1 className="text-5xl font-bold text-white mb-2">
                    Kingdom 2358
                  </h1>
                  <p className="text-xl text-blue-100">
                    Asahikawa Performance Hub
                  </p>
                  <div className="flex items-center mt-3 text-blue-200">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                    <span className="text-sm">Live Dashboard</span>
                  </div>
                </div>
              </div>

              <div className="text-right">
                <div className="text-blue-100 text-sm mb-2">
                  Last updated: {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}
                </div>
                {activeKvK && (
                  <div className="bg-white/20 backdrop-blur-sm rounded-xl px-4 py-2">
                    <div className="text-white font-semibold">Active KvK</div>
                    <div className="text-blue-100 text-sm">{activeKvK.name} (Season {activeKvK.season})</div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 -mt-8 -mr-8 w-32 h-32 opacity-20">
            <FaArrowUp className="w-full h-full transform rotate-45" />
          </div>
          <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-24 h-24 opacity-10">
            <FaFistRaised className="w-full h-full transform -rotate-12" />
          </div>
        </div>

      {/* Summary Cards */}
      <section className="mb-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Power"
          value={kingdomStats.totalPower > 0 ? kingdomStats.totalPower : 'No data'}
          icon={<FaShieldAlt className="h-6 w-6" />}
          formatter={kingdomStats.totalPower > 0 ? formatNumber : undefined}
        />
        <StatCard
          title="Kill Points"
          value={kingdomStats.totalKillPoints > 0 ? kingdomStats.totalKillPoints : 'No data'}
          icon={<FaFistRaised className="h-6 w-6" />}
          formatter={kingdomStats.totalKillPoints > 0 ? formatNumber : undefined}
        />
        <StatCard
          title="Dead Troops"
          value={kingdomStats.totalDeads > 0 ? kingdomStats.totalDeads : 'No data'}
          icon={<FaSkullCrossbones className="h-6 w-6" />}
          formatter={kingdomStats.totalDeads > 0 ? formatNumber : undefined}
        />
        <StatCard
          title="Active Players"
          value={kingdomStats.totalPlayers > 0 ? `${kingdomStats.activePlayers}/${kingdomStats.totalPlayers}` : 'No data'}
          icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" /></svg>}
        />
      </section>

      {/* Enhanced Player Performance Highlights */}
      <section className="mb-12">
        <div className="flex items-center mb-8">
          <div className={`p-3 rounded-xl mr-4 ${
            theme === 'light' ? 'bg-gradient-to-r from-yellow-400 to-orange-500' : 'bg-gradient-to-r from-yellow-500 to-orange-600'
          }`}>
            <FaArrowUp className="text-2xl text-white" />
          </div>
          <div>
            <h2 className={`text-3xl font-bold ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
              Top Performers
            </h2>
            <p className={`text-lg ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              Leading players in the current period
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {topPowerGainer && (
            <div className={`group relative overflow-hidden rounded-2xl shadow-xl transition-all duration-300 hover:scale-105 ${
              theme === 'light'
                ? 'bg-gradient-to-br from-green-50 to-emerald-100 hover:shadow-2xl'
                : 'bg-gradient-to-br from-green-900/30 to-emerald-900/30 hover:bg-green-900/40'
            }`}>
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10"></div>
              <div className="relative p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-xl bg-green-500 mr-4">
                    <FaArrowUp className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className={`text-lg font-bold ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
                      Power Champion
                    </h3>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
                      {topPowerGainer.name}
                    </p>
                    <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                      [{topPowerGainer.alliance || 'No Alliance'}]
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-3xl font-bold text-green-600 mb-1">
                    +{formatNumber(topPowerGainer.power)}
                  </p>
                  <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                    Power Gained
                  </p>
                </div>
              </div>
            </div>
          )}

          {topKillsGainer && (
            <div className={`group relative overflow-hidden rounded-2xl shadow-xl transition-all duration-300 hover:scale-105 ${
              theme === 'light'
                ? 'bg-gradient-to-br from-red-50 to-pink-100 hover:shadow-2xl'
                : 'bg-gradient-to-br from-red-900/30 to-pink-900/30 hover:bg-red-900/40'
            }`}>
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-pink-500/10"></div>
              <div className="relative p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-xl bg-red-500 mr-4">
                    <FaFistRaised className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className={`text-lg font-bold ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
                      Kill Master
                    </h3>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
                      {topKillsGainer.name}
                    </p>
                    <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                      [{topKillsGainer.alliance || 'No Alliance'}]
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-3xl font-bold text-red-600 mb-1">
                    +{formatNumber(topKillsGainer.killPoints)}
                  </p>
                  <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                    Kill Points
                  </p>
                </div>
              </div>
            </div>
          )}

          {topT45KillsGainer && (
            <div className={`group relative overflow-hidden rounded-2xl shadow-xl transition-all duration-300 hover:scale-105 ${
              theme === 'light'
                ? 'bg-gradient-to-br from-purple-50 to-indigo-100 hover:shadow-2xl'
                : 'bg-gradient-to-br from-purple-900/30 to-indigo-900/30 hover:bg-purple-900/40'
            }`}>
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-indigo-500/10"></div>
              <div className="relative p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-xl bg-purple-500 mr-4">
                    <FaShieldAlt className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className={`text-lg font-bold ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
                      Elite Hunter
                    </h3>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
                      {topT45KillsGainer.name}
                    </p>
                    <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                      [{topT45KillsGainer.alliance || 'No Alliance'}]
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-3xl font-bold text-purple-600 mb-1">
                    +{formatNumber(topT45KillsGainer.t45Kills)}
                  </p>
                  <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                    T4/T5 Kills
                  </p>
                </div>
              </div>
            </div>
          )}

          {topDeadsGainer && (
            <div className={`group relative overflow-hidden rounded-2xl shadow-xl transition-all duration-300 hover:scale-105 ${
              theme === 'light'
                ? 'bg-gradient-to-br from-yellow-50 to-orange-100 hover:shadow-2xl'
                : 'bg-gradient-to-br from-yellow-900/30 to-orange-900/30 hover:bg-yellow-900/40'
            }`}>
              <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-orange-500/10"></div>
              <div className="relative p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-xl bg-yellow-500 mr-4">
                    <FaSkullCrossbones className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className={`text-lg font-bold ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
                      Brave Warrior
                    </h3>
                    <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
                      {topDeadsGainer.name}
                    </p>
                    <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                      [{topDeadsGainer.alliance || 'No Alliance'}]
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-3xl font-bold text-yellow-600 mb-1">
                    {formatNumber(topDeadsGainer.deads)}
                  </p>
                  <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                    Dead Troops
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Active KvK Dashboard */}
      {activeKvK && (
        <section className="mb-10">
          <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
            Active KvK: {activeKvK.name}
          </h2>
          <KvKDashboard
            kvk={activeKvK}
            latestScan={latestScan}
            previousScan={activeKvKScans[1]}
            baselineScan={baselineScan}
          />
        </section>
      )}

      {/* Leaderboards */}
      <section className="mb-10">
        <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
          Leaderboards
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Leaderboard
            title="Top Players by Power"
            players={topPowerPlayers}
            valueKey="power"
            valueFormatter={(val) => formatNumber(val)}
          />
          <Leaderboard
            title="Top Players by Kills"
            players={topKillsPlayers}
            valueKey="killPoints"
            valueFormatter={(val) => formatNumber(val)}
          />
          <Leaderboard
            title="Top Players by T4-5 Kills"
            players={topT45KillsPlayers}
            valueKey="t45Kills"
            valueFormatter={(val) => formatNumber(val)}
          />
          <Leaderboard
            title="Top Players by Dead Troops"
            players={topDeadsPlayers}
            valueKey="deads"
            valueFormatter={(val) => formatNumber(val)}
          />
        </div>
      </section>

      {/* Admin Actions */}
      {(canUploadScans || canManageKvK) && (
        <section className="mb-10">
          <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
            Admin Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {canUploadScans && (
              <Link
                to="/upload-scan"
                className={`p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow ${
                  theme === 'light' ? 'bg-white hover:bg-gray-50' : 'bg-gray-800 hover:bg-gray-700'
                }`}
              >
                <h3 className={`text-lg font-semibold mb-2 ${theme === 'light' ? 'text-blue-600' : 'text-blue-400'}`}>
                  Upload General Scan
                </h3>
                <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                  Upload player statistics not tied to any specific KvK
                </p>
              </Link>
            )}

            {canManageKvK && (
              <Link
                to="/create-kvk"
                className={`p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow ${
                  theme === 'light' ? 'bg-white hover:bg-gray-50' : 'bg-gray-800 hover:bg-gray-700'
                }`}
              >
                <h3 className={`text-lg font-semibold mb-2 ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`}>
                  Create KvK
                </h3>
                <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                  Set up a new Kingdom vs Kingdom event
                </p>
              </Link>
            )}

            <Link
              to="/scans"
              className={`p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow ${
                theme === 'light' ? 'bg-white hover:bg-gray-50' : 'bg-gray-800 hover:bg-gray-700'
              }`}
            >
              <h3 className={`text-lg font-semibold mb-2 ${theme === 'light' ? 'text-purple-600' : 'text-purple-400'}`}>
                View All Scans
              </h3>
              <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                Browse and manage all uploaded scan data
              </p>
            </Link>
          </div>
        </section>
      )}
      </div>
    </div>
  );
};

export default DashboardPage;
