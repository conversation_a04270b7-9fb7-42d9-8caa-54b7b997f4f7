import React, { memo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import {
  BarChart, Bar, <PERSON>A<PERSON>s, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>, <PERSON>, Cell
} from 'recharts';

interface ChartData {
  name: string;
  value: number;
}

interface ChartCardProps {
  title: string;
  data: ChartData[];
  type: 'bar' | 'pie';
  dataKey?: string;
  colors?: string[];
}

const ChartCard: React.FC<ChartCardProps> = ({ 
  title, 
  data, 
  type = 'bar',
  dataKey = 'value',
  colors = ['#3b82f6', '#10b981', '#f59e0b', '#ec4899', '#8b5cf6', '#ef4444']
}) => {
  const { theme } = useTheme();
  
  const renderChart = () => {
    if (type === 'bar') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart
            data={data}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke={theme === 'light' ? '#e5e7eb' : '#374151'} />
            <XAxis 
              dataKey="name" 
              tick={{ fill: theme === 'light' ? '#4b5563' : '#9ca3af' }}
            />
            <YAxis 
              tick={{ fill: theme === 'light' ? '#4b5563' : '#9ca3af' }}
              tickFormatter={(value) => {
                if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
                if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
                return value.toString();
              }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: theme === 'light' ? '#ffffff' : '#1f2937',
                borderColor: theme === 'light' ? '#e5e7eb' : '#374151',
                color: theme === 'light' ? '#111827' : '#f9fafb',
              }}
              formatter={(value: number) => {
                if (value >= 1000000) return [`${(value / 1000000).toFixed(2)}M`, ''];
                if (value >= 1000) return [`${(value / 1000).toFixed(1)}K`, ''];
                return [value, ''];
              }}
            />
            <Legend wrapperStyle={{ color: theme === 'light' ? '#4b5563' : '#9ca3af' }} />
            <Bar dataKey={dataKey} fill="#3b82f6" />
          </BarChart>
        </ResponsiveContainer>
      );
    } else if (type === 'pie') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={100}
              fill="#8884d8"
              dataKey={dataKey}
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
            <Tooltip
              contentStyle={{
                backgroundColor: theme === 'light' ? '#ffffff' : '#1f2937',
                borderColor: theme === 'light' ? '#e5e7eb' : '#374151',
                color: theme === 'light' ? '#111827' : '#f9fafb',
              }}
              formatter={(value: number) => {
                if (value >= 1000000) return [`${(value / 1000000).toFixed(2)}M`, ''];
                if (value >= 1000) return [`${(value / 1000).toFixed(1)}K`, ''];
                return [value, ''];
              }}
            />
            <Legend wrapperStyle={{ color: theme === 'light' ? '#4b5563' : '#9ca3af' }} />
          </PieChart>
        </ResponsiveContainer>
      );
    }
    
    return null;
  };
  
  return (
    <div className={`p-4 rounded-lg shadow-md ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
      <h3 className={`text-lg font-semibold mb-4 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
        {title}
      </h3>
      {renderChart()}
    </div>
  );
};

export default memo(ChartCard);
