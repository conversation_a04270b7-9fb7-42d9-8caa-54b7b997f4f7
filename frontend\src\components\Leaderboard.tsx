import React, { memo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { PlayerScanData } from '../types/dataTypes';

interface LeaderboardProps {
  title: string;
  players: PlayerScanData[];
  valueKey: keyof PlayerScanData;
  valueFormatter?: (value: number) => string;
}

const Leaderboard: React.FC<LeaderboardProps> = ({
  title,
  players,
  valueKey,
  valueFormatter = (value: number) => value.toLocaleString()
}) => {
  const { theme } = useTheme();

  return (
    <div className={`rounded-lg overflow-hidden shadow-md ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
      <div className={`px-4 py-3 ${theme === 'light' ? 'bg-blue-50' : 'bg-blue-900/30'}`}>
        <h3 className={`font-semibold ${theme === 'light' ? 'text-blue-700' : 'text-blue-300'}`}>{title}</h3>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className={`${theme === 'light' ? 'bg-gray-50' : 'bg-gray-900/50'}`}>
            <tr>
              <th scope="col" className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                Rank
              </th>
              <th scope="col" className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                Player
              </th>
              <th scope="col" className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                Alliance
              </th>
              <th scope="col" className={`px-4 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                {valueKey === 'power' ? 'Power' :
                 valueKey === 'killPoints' ? 'Kill Points' :
                 valueKey === 'deads' ? 'Dead Troops' :
                 valueKey === 't1Kills' ? 'T1 Kills' :
                 valueKey === 't2Kills' ? 'T2 Kills' :
                 valueKey === 't3Kills' ? 'T3 Kills' :
                 valueKey === 't4Kills' ? 'T4 Kills' :
                 valueKey === 't5Kills' ? 'T5 Kills' :
                 valueKey === 'totalKills' ? 'Total Kills' :
                 valueKey === 't45Kills' ? 'T4-5 Kills' :
                 valueKey === 'ranged' ? 'Ranged' :
                 valueKey === 'rssGathered' ? 'RSS Gathered' :
                 valueKey === 'rssAssisted' ? 'RSS Assisted' :
                 valueKey === 'helps' ? 'Helps' :
                 String(valueKey)}
              </th>
            </tr>
          </thead>
          <tbody className={`${theme === 'light' ? 'bg-white' : 'bg-gray-800'} divide-y ${theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'}`}>
            {players.map((player, index) => (
              <tr key={player.governorId || index} className={`${theme === 'light' ? 'hover:bg-gray-50' : 'hover:bg-gray-700'}`}>
                <td className={`px-4 py-2 whitespace-nowrap text-sm font-medium ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  {index + 1}
                </td>
                <td className={`px-4 py-2 whitespace-nowrap text-sm ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  {player.name}
                </td>
                <td className={`px-4 py-2 whitespace-nowrap text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                  <span className="px-2 py-1 text-xs rounded-full bg-opacity-50 font-medium"
                    style={{
                      backgroundColor: getAllianceColor(player.alliance || 'N/A', theme === 'light' ? 0.2 : 0.3),
                      color: getAllianceColor(player.alliance || 'N/A', 1)
                    }}>
                    {player.alliance || 'N/A'}
                  </span>
                </td>
                <td className={`px-4 py-2 whitespace-nowrap text-sm text-right ${theme === 'light' ? 'text-gray-900' : 'text-white'} font-medium`}>
                  {valueFormatter(player[valueKey] as number)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Helper function to get a consistent color for each alliance
const getAllianceColor = (alliance: string, opacity: number = 1): string => {
  const colors: Record<string, string> = {
    'ASAH': `rgba(59, 130, 246, ${opacity})`, // blue
    'KAWA': `rgba(16, 185, 129, ${opacity})`, // green
    'HOKU': `rgba(245, 158, 11, ${opacity})`, // amber
    'NIJI': `rgba(236, 72, 153, ${opacity})`, // pink
    'YUKI': `rgba(139, 92, 246, ${opacity})`, // purple
    'KAZE': `rgba(239, 68, 68, ${opacity})`, // red
  };

  return colors[alliance] || `rgba(107, 114, 128, ${opacity})`; // gray default
};

export default memo(Leaderboard);
