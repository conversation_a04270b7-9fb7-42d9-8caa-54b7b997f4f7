from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

# --- Player Schemas ---
class PlayerBase(BaseModel):
    name: str
    governor_id: Optional[str] = None
    alliance: Optional[str] = None

class PlayerCreate(PlayerBase):
    pass

class Player(PlayerBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# --- Scan <PERSON>hemas ---
class ScanBase(BaseModel):
    name: str
    is_baseline: Optional[bool] = False
    kvk_id: Optional[int] = None
    kvk_phase: Optional[str] = None

class ScanCreate(ScanBase):
    pass

class Scan(ScanBase):
    id: int
    timestamp: datetime

    class Config:
        from_attributes = True

# --- PlayerStat Schemas ---
class PlayerStatBase(BaseModel):
    power: int
    kill_points_t1: Optional[int] = 0
    kill_points_t2: Optional[int] = 0
    kill_points_t3: Optional[int] = 0
    kill_points_t4: Optional[int] = 0
    kill_points_t5: Optional[int] = 0
    total_kill_points: int
    t45_kills: Optional[int] = 0
    ranged: Optional[int] = 0
    rss_gathered: Optional[int] = 0 # Pydantic int handles large numbers
    rss_assistance: Optional[int] = 0 # Pydantic int handles large numbers
    helps: Optional[int] = 0
    dead_troops: int

class PlayerStatCreate(PlayerStatBase):
    player_id: Optional[int] = None # For creating alongside player or linking existing
    player_name: Optional[str] = None # For matching/creating player during scan upload
    governor_id: Optional[str] = None # For matching/creating player
    alliance: Optional[str] = None # For updating player info
    # New fields from PlayerStatBase are inherited

class PlayerStat(PlayerStatBase):
    id: int
    player_id: int
    scan_id: int
    timestamp: datetime
    player: Player # Nested player info

    class Config:
        from_attributes = True

class PlayerStatWithScanInfo(PlayerStatBase):
    id: int
    player_id: int
    scan_id: int
    timestamp: datetime
    player: Player # Nested player info
    scan: Scan # Nested scan info

    class Config:
        from_attributes = True

# --- DeltaStat Schemas ---
class DeltaStatBase(BaseModel):
    power_delta: int
    kill_points_delta: int
    dead_troops_delta: int
    kp_per_dead: Optional[float] = None
    is_zeroed: Optional[bool] = False
    meets_kp_target: Optional[bool] = False
    meets_dead_target: Optional[bool] = False

class DeltaStatCreate(DeltaStatBase):
    player_id: int
    start_scan_id: int
    end_scan_id: int

class DeltaStat(DeltaStatBase):
    id: int
    player_id: int
    start_scan_id: int
    end_scan_id: int
    timestamp: datetime
    player: Player # Nested player info

    class Config:
        from_attributes = True

# --- KvK Schemas ---
class KvKBase(BaseModel):
    name: str
    start_date: datetime
    end_date: Optional[datetime] = None
    status: Optional[str] = "upcoming"
    season: int

class KvKCreate(KvKBase):
    pass

class KvKUpdate(BaseModel):
    name: Optional[str] = None
    end_date: Optional[datetime] = None
    status: Optional[str] = None

class KvK(KvKBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# --- Parameter Schemas ---
class ParameterBase(BaseModel):
    name: str
    value: float
    description: Optional[str] = None

class ParameterCreate(ParameterBase):
    pass

class ParameterUpdate(BaseModel):
    value: float

class Parameter(ParameterBase):
    id: int
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# --- Scan Upload Schemas ---
class ScanDataItem(BaseModel):
    player_name: str
    power: int
    total_kill_points: int
    dead_troops: int
    alliance: Optional[str] = None # Standardized to 'alliance'
    governor_id: Optional[str] = None
    kill_points_t1: Optional[int] = 0
    kill_points_t2: Optional[int] = 0
    kill_points_t3: Optional[int] = 0
    kill_points_t4: Optional[int] = 0
    kill_points_t5: Optional[int] = 0
    t45_kills: Optional[int] = 0
    ranged: Optional[int] = 0
    rss_gathered: Optional[int] = 0
    rss_assistance: Optional[int] = 0
    helps: Optional[int] = 0

class ScanUpload(BaseModel):
    scan_name: str # e.g. "Pre-KVK", "Scan 1"
    is_baseline: Optional[bool] = False
    kvk_id: Optional[int] = None
    data: List[ScanDataItem]

# --- Report Schemas (Examples) ---
class PlayerPerformanceSummary(PlayerBase):
    current_power: int
    current_kp: int
    current_dead: int
    kp_gain: int
    dead_gain: int
    power_loss: int
    kp_per_dead: Optional[float] = None
    is_zeroed: bool
    meets_kp_target: bool
    meets_dead_target: bool

class LeaderboardEntry(BaseModel):
    rank: int
    player_name: str
    alliance: Optional[str]
    value: float # Could be KP, Dead, KP Gain, etc.

class FullReport(BaseModel):
    top_kp: List[LeaderboardEntry]
    top_dead: List[LeaderboardEntry]
    # ... other leaderboards
    underperformers: List[PlayerPerformanceSummary]
    penalty_summary: List[PlayerPerformanceSummary] # Or a more specific penalty model